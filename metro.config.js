const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');
const { withSentryConfig } = require('@sentry/react-native/metro');

const defaultConfig = getDefaultConfig(__dirname);

const customConfig = {
  transformer: {
    babelTransformerPath: require.resolve('react-native-sass-transformer'),
  },
  resolver: {
    assetExts: defaultConfig.resolver.assetExts.filter(
      (ext) => ext !== 'scss' && ext !== 'sass',
    ),
    sourceExts: [...defaultConfig.resolver.sourceExts, 'scss', 'sass'],
  },
};

module.exports = withSentryConfig(mergeConfig(defaultConfig, customConfig));
