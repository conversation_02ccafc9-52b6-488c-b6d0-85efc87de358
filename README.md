# Getting started
This will take you through instructions to build and run YPD mobile app on iOS and Android platforms.

## iOS Development
This project requires `node^21`, `java^17`, `cocoapods >=1.13`, `react-native^0.76.1` to be installed on your machine before we move on.

In order to build and run iOS app, you will also need to have XCode app installed from AppStore with at-least one simulator running `ios:12` or later.

Once you have installed above items, then you get started with the following steps to complete development setup for iOS app:

1. Clone this project into your system
2. Then run `cp .env.example .env`
3. Configure backend environment by placing its `aws-exports.js` file into `src/` folder (Please reach out to your contact person to get this file)
4. Open a terminal session and run `yarn install --frozen-lockfile` to install node dependencies
5. Now, change the directory to `ios/` (i.e. `cd ios/`)
6. Next, please install Pods by running `pod install` into the same shell
7. Now, change directory back and run `yarn run start` which starts a node service on port :8081 that is serving the react-native app build
8. Now, press `i` to start the iOS app
9. If the above step does not work, run `yarn run ios` in another terminal in the `ios` directory

This should open up the simulator and YPD app will be installed and launched into the simulator.

## Android Development

1. Clone this project into your system
2. Then run `cp .env.example .env`
3. Configure backend environment by placing its `aws-exports.js` file into `src/` folder (Please reach out to your contact person to get this file)
4. Open a terminal session and run `yarn install --frozen-lockfile` to install node dependencies
5. Next, please run `yarn run start` which starts a node service on port :8081 that is serving the react-native app build
6. Now, press `a` to start the Android app
7. If the above step does not work, run `yarn run android` in another terminal

This should open up the emulator and YPD app will be installed and launched into the emulator.

## Linting and Pre-Commit Hooks

This project uses ESLint and Prettier to maintain code quality and formatting consistency. Additionally, Husky is configured to enforce these standards automatically during the pre-commit process.

### Pre-Commit Hooks with Husky

Husky is set up to run linting and formatting tasks before committing code to the repository. This ensures that only properly formatted and linted code is committed.

### Linting with ESLint

ESLint is used to identify and report on patterns found in JavaScript and TypeScript code, helping to ensure code quality and prevent errors.

To manually run the linter, you can use the following command:

  ```bash
  yarn lint
  ```
### Code Formatting with Prettier

Prettier is used to automatically format code to ensure consistency in style across the project.

To format the code manually, use the following command:

  ```bash
  yarn format
```
