# Profile Completion Flow - Testing Guide

## Test Scenarios

### 1. **Complete Profile Flow**
**Setup:** User with incomplete profile (missing name, age, or gender)
**Steps:**
1. Login with existing account
2. Verify warning message appears: "Profile not created yet. please create your profile first."
3. Wait 3 seconds for automatic navigation to ManageAccount
4. Verify screen shows "Complete your Profile" heading
5. Verify no back button is visible
6. Verify no "Delete Account" button is visible
7. Try hardware back button (Android) - should be disabled
8. Fill in required fields (name, age, gender)
9. Click "Update Profile"
10. Verify navigation to home screen
11. Verify user cannot navigate back to profile completion

### 2. **Validation Testing**
**Setup:** User in profile completion mode
**Steps:**
1. Leave name field empty, try to update
2. Verify validation alert appears
3. Leave age unselected, try to update
4. Verify validation alert appears
5. Leave gender unselected, try to update
6. Verify validation alert appears

### 3. **Complete Profile (No Issues)**
**Setup:** User with complete profile
**Steps:**
1. <PERSON><PERSON> with account that has name, age, and gender
2. Verify direct navigation to home screen
3. No profile completion flow should trigger

### 4. **Normal ManageAccount Access**
**Setup:** User accessing from Settings
**Steps:**
1. Navigate to Settings → Manage Account
2. Verify "Manage your Account" heading
3. Verify back button is visible
4. Verify "Delete Account" button is visible
5. Verify normal navigation behavior

## Required Fields for Complete Profile
- `name`: Must not be empty or null
- `age`: Must not be empty or null
- `gender`: Must not be empty or null

## Error Scenarios to Test
1. Network failure during profile update
2. Invalid profile data
3. User force-closing app during profile completion
4. Multiple rapid taps on Update Profile button

## Expected Behavior
- ✅ Users cannot bypass profile completion
- ✅ Required fields are enforced
- ✅ Smooth navigation flow
- ✅ Clear user feedback
- ✅ Proper error handling
