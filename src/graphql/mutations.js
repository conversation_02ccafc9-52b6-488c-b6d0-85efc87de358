export const createProfile = /* GraphQL */ `
  mutation CreateProfile(
    $input: CreateProfileInput!
    $condition: ModelProfileConditionInput
  ) {
    createProfile(input: $input, condition: $condition) {
      userId
      platform
      release
      email
      username
      name
      age
      gender
      problems
      customProblems
      sideEffects
      customSideEffects
      initialDose
      showNotifications
      privacyTermsAccepted
      safetyCheckAccepted
      dosageType
      cannabisType
      ratingModalShownAt
      confirmationCodeCount
      confirmationCodeSentAt
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const updateProfile = /* GraphQL */ `
  mutation UpdateProfile(
    $input: UpdateProfileInput!
    $condition: ModelProfileConditionInput
  ) {
    updateProfile(input: $input, condition: $condition) {
      userId
      platform
      release
      email
      username
      name
      age
      gender
      problems
      customProblems
      sideEffects
      customSideEffects
      initialDose
      showNotifications
      privacyTermsAccepted
      safetyCheckAccepted
      dosageType
      cannabisType
      ratingModalShownAt
      confirmationCodeCount
      confirmationCodeSentAt
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const createSuggestion = /* GraphQL */ `
  mutation CreateSuggestion(
    $input: CreateSuggestionInput!
    $condition: ModelSuggestionConditionInput
  ) {
    createSuggestion(input: $input, condition: $condition) {
      id
      userId
      release
      username
      platform
      thco
      thci
      cbdo
      cbdi
      rthco
      rthci
      rcbdo
      rcbdi
      notes
      strain
      effects
      sideEffects
      overallEffect
      overallSideEffect
      loopN
      checkinType
      suggestionType
      dosageType
      cannabisType
      problemSetType
      userQuits
      userDisabled
      hitCBDMax
      hitTHCMax
      isBELimitExceeded
      createdAt
      updatedAt
      checkinAt
      suggestionAt
      __typename
    }
  }
`;

export const createEvent = /* GraphQL */ `
  mutation CreateEvent(
    $input: CreateEventInput!
    $condition: ModelEventConditionInput
  ) {
    createEvent(input: $input, condition: $condition) {
      id
      name
      code
      status
      release
      username
      platform
      attributes
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const createReport = /* GraphQL */ `
  mutation CreateReport(
    $input: CreateReportInput!
    $condition: ModelReportConditionInput
  ) {
    createReport(input: $input, condition: $condition) {
      name
      release
      platform
      username
      reportURL
      reportType
      slackResponseURL
      endDate
      startDate
      createdAt
      id
      updatedAt
      __typename
    }
  }
`;

export const deleteProfile = /* GraphQL */ `
  mutation DeleteProfile(
    $input: DeleteProfileInput!
    $condition: ModelProfileConditionInput
  ) {
    deleteProfile(input: $input, condition: $condition) {
      userId
      platform
      release
      email
      username
      name
      age
      gender
      problems
      customProblems
      sideEffects
      customSideEffects
      initialDose
      showNotifications
      dosageType
      cannabisType
      confirmationCodeCount
      confirmationCodeSentAt
      createdAt
      updatedAt
      __typename
    }
  }
`;
