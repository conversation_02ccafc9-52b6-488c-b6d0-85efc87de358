import { createSuggestion } from '../graphql/mutations';
import { generateClient } from 'aws-amplify/api';
import { getDevicePlatform } from '../utils/utils';
import { suggestionsByUserId } from '../graphql/queries';

const client = generateClient();
const AUTH_MODE = process.env.AUTH_MODE || 'apiKey';

export const createDoseSuggestion = async (suggestionData) => {
  try {
    const updatedData = {
      ...suggestionData,
      createdAt: new Date(),
      updatedAt: new Date(),
      checkinAt: new Date(),
      problemSetType: 'general',
      release: process.env.RELEASE,
      platform: getDevicePlatform(),
    };
    console.log('Creating suggestion for:', updatedData);

    await client.graphql({
      query: createSuggestion,
      variables: { input: updatedData },
      authMode: AUTH_MODE,
    });

    console.log('Suggestion created.');
  } catch (error) {
    console.error('Error creating suggestion:', error);
    throw error;
  }
};

export const getSuggestions = async (userId) => {
  try {
    const response = await client.graphql({
      query: suggestionsByUserId,
      variables: {
        userId,
        // limit: 100,
      },
      authMode: AUTH_MODE,
    });
    const items = response.data?.suggestionsByUserId?.items || [];
    console.log(items.length, 'suggestions retrieved.');
    return items.sort(
      (a, b) => new Date(b.suggestionAt) - new Date(a.suggestionAt),
    );
  } catch (error) {
    console.error('Error retrieving suggestions:', error);
    throw error;
  }
};
