import {
  createProfile,
  updateProfile,
  deleteProfile,
} from '../graphql/mutations';
import { generateClient } from 'aws-amplify/api';
import { getDevicePlatform } from '../utils/utils';
import { getProfile } from '../graphql/queries';
import { deleteUser } from 'aws-amplify/auth';

const client = generateClient();
const AUTH_MODE = process.env.AUTH_MODE || 'apiKey';

export const createUserProfile = async (profileData) => {
  try {
    const data = {
      ...profileData,
      initialDose: JSON.stringify(profileData.initialDose),
      confirmationCodeCount: JSON.stringify({}),
      confirmationCodeSentAt: new Date(),
      release: process.env.RELEASE,
      platform: getDevicePlatform(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const response = await client.graphql({
      query: createProfile,
      variables: { input: data },
      authMode: AUTH_MODE,
    });

    console.log('Profile created:', response.data.createProfile);
  } catch (error) {
    console.error('Error creating profile:', error);
    throw error;
  }
};
export const updateUserProfile = async (profileData) => {
  try {
    const data = {
      ...profileData,
      initialDose: JSON.stringify(profileData.initialDose),
      confirmationCodeCount: JSON.stringify({}),
      updatedAt: new Date(),
    };

    const response = await client.graphql({
      query: updateProfile,
      variables: { input: data },
      authMode: AUTH_MODE,
    });

    console.log('Profile updated:', response.data.updateProfile);
  } catch (error) {
    console.error('Error updating profile:', error);
  }
};

export const getUserProfile = async (userId) => {
  try {
    const response = await client.graphql({
      query: getProfile,
      variables: { userId },
      authMode: AUTH_MODE,
    });

    console.log('Profile retrieved:', response.data.getProfile);
    return response.data.getProfile;
  } catch (error) {
    console.error('Error retrieving profile:', error);
    throw error;
  }
};

export const deleteUserProfile = async (profile) => {
  try {
    const response = await client.graphql({
      query: deleteProfile,
      variables: { input: { userId: profile.userId } },
      authMode: AUTH_MODE,
    });
    console.log('Profile deleted:', response.data.deleteProfile);

    await deleteUser();
    return response.data.deleteProfile;
  } catch (error) {
    console.error('Error deleting user account:', error);
    throw error;
  }
};
