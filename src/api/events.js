import { createEvent } from '../graphql/mutations';
import { generateClient } from 'aws-amplify/api';
import { getDevicePlatform } from '../utils/utils';

const client = generateClient();
const AUTH_MODE = process.env.AUTH_MODE || 'apiKey';

export const recordEvent = async (
  name,
  username,
  status,
  code,
  attributes,
  createdAt,
) => {
  let input;
  try {
    input = {
      name: name,
      code: code,
      status: status,
      username: username,
      createdAt: createdAt,
      release: process.env.RELEASE,
      platform: getDevicePlatform(),
      attributes: JSON.stringify(attributes),
    };

    const response = await client.graphql({
      query: createEvent,
      variables: { input },
      authMode: AUTH_MODE,
    });
    console.log(`${code} ${name} recorded for user ${username}`, response);
  } catch (error) {
    console.error(
      `Failed to record ${code} ${name} for user ${username}`,
      error,
      input,
    );
  }
};
