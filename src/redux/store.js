import { combineReducers } from 'redux';
import {
  persistStore,
  persistReducer,
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
} from 'redux-persist';
import AsyncStorage from '@react-native-async-storage/async-storage';

import appearanceReducer from './slices/appearanceSlice';
import profileReducer from './slices/profileSlice';
import checkInReducer from './slices/checkinSlice';
import suggestionsReducer from './slices/suggestionSlice';
import notifications from './slices/notificationSlice';
import events from './slices/eventsSlice';
import { configureStore } from '@reduxjs/toolkit';

const persistConfig = {
  key: 'root',
  storage: AsyncStorage,
};

const appReducer = combineReducers({
  events: events,
  checkIn: checkInReducer,
  profile: profileReducer,
  notifications: notifications,
  suggestions: suggestionsReducer,
  appearance: appearanceReducer,
});

const rootReducer = (state, action) => {
  if (action.type === 'RESET_STORE') {
    console.log('Store has been reset.');
    return appReducer(undefined, action);
  }
  return appReducer(state, action);
};

export const resetAppState = async (dispatch) => {
  try {
    await persistor.purge();
    dispatch(resetStore());
    await AsyncStorage.clear();
  } catch (error) {
    console.error('Failed to reset app state:', error);
  }
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // Ignore redux-persist action types
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    }),
});

export const persistor = persistStore(store);

export const resetStore = () => ({ type: 'RESET_STORE' });
