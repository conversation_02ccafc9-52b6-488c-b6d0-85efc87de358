import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  userId: '',
  name: '',
  username: '',
  email: '',
  age: '',
  gender: '',
  problems: [],
  customProblems: [],
  cannabisType: 'both',
  dosageType: 'both',
  initialDose: {
    thcVape: 0,
    thcOral: 0,
    cbdVape: 0,
    cbdOral: 0,
    strain: '',
  },
  showNotifications: true,
  safetyCheckAccepted: false,
  privacyTermsAccepted: false,
  sideEffects: [],
  customSideEffects: [],
};

const profileSlice = createSlice({
  name: 'profile',
  initialState,
  reducers: {
    setName: (state, action) => {
      state.name = action.payload;
    },
    setUserId: (state, action) => {
      state.userId = action.payload;
    },
    setEmailAddress: (state, action) => {
      state.email = action.payload;
    },
    setUsername: (state, action) => {
      state.username = action.payload;
    },
    setAge: (state, action) => {
      state.age = action.payload;
    },
    setGender: (state, action) => {
      state.gender = action.payload;
    },
    setSafetyCheckAccepted: (state, action) => {
      state.safetyCheckAccepted = action.payload;
    },
    setPrivacyTermsAccepted: (state, action) => {
      state.privacyTermsAccepted = action.payload;
    },
    setProblems: (state, action) => {
      state.problems = action.payload;
    },
    setCustomProblems: (state, action) => {
      state.customProblems = action.payload;
    },
    setSideEffects: (state, action) => {
      state.sideEffects = action.payload;
    },
    setCustomSideEffects: (state, action) => {
      state.customSideEffects = action.payload;
    },
    setCannabisType: (state, action) => {
      state.cannabisType = action.payload;
    },
    setDosageType: (state, action) => {
      state.dosageType = action.payload;
    },
    setShowNotifications: (state, action) => {
      state.showNotifications = action.payload;
    },
    setDosage(state, action) {
      state.initialDose = {
        ...state.initialDose,
        ...action.payload,
      };
    },
    updateProfile: (state, action) => {
      const payload = {
        ...action.payload,
        initialDose: JSON.parse(action.payload.initialDose),
        confirmationCodeCount: JSON.parse(action.payload.confirmationCodeCount),
      };
      delete payload.__typename;

      return { ...state, ...payload };
    },
  },
});

export const {
  setName,
  setUserId,
  setUsername,
  setEmailAddress,
  setAge,
  setGender,
  setProblems,
  setDosage,
  setShowNotifications,
  setCustomProblems,
  setCannabisType,
  setDosageType,
  setSafetyCheckAccepted,
  setPrivacyTermsAccepted,
  updateProfile,
  setSideEffects,
  setCustomSideEffects,
} = profileSlice.actions;
export default profileSlice.reducer;
