import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  profileBuilding: {},
};

const eventsSlice = createSlice({
  name: 'events',
  initialState,
  reducers: {
    addProfileBuildingEvent(state, action) {
      const { code, value } = action.payload;
      state.profileBuilding[code] = value;
    },
  },
});

export const { addProfileBuildingEvent } = eventsSlice.actions;
export default eventsSlice.reducer;
