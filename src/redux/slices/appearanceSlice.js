import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  selectedTheme: 'light',
  systemDefault: false,
};

const appearanceSlice = createSlice({
  name: 'appearance',
  initialState,
  reducers: {
    setSelectedTheme: (state, action) => {
      state.selectedTheme = action.payload;
    },
    setSystemDefault(state, action) {
      state.systemDefault = action.payload;
    },
  },
});

export const { setSelectedTheme, setSystemDefault } = appearanceSlice.actions;
export default appearanceSlice.reducer;
