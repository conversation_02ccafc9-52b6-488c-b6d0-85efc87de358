import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  problemLevel: {},
  overallEffect: 0,
  sideEffects: [],
  customSideEffects: [],
  overAllSideEffect: 0,
};

const checkinSlice = createSlice({
  name: 'checkIn',
  initialState,
  reducers: {
    setEffects: (state, action) => {
      state.problemLevel = action.payload;
    },
    setOverallEffect: (state, action) => {
      state.overallEffect = action.payload;
    },
    setOverAllSideEffect: (state, action) => {
      state.overAllSideEffect = action.payload;
    },
    setSideEffects: (state, action) => {
      state.sideEffects = action.payload;
    },
    setCustomSideEffects: (state, action) => {
      state.customSideEffects = action.payload;
    },
    resetCheckinState: () => initialState,
  },
});

export const {
  setEffects,
  setOverallEffect,
  setSideEffects,
  setOverAllSideEffect,
  setCustomSideEffects,
  resetCheckinState,
} = checkinSlice.actions;
export default checkinSlice.reducer;
