import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  checkinStats: {},
  suggestions: [],
  lastSuggestion: {},
  thciLastSuggestion: {},
  thcoLastSuggestion: {},
  cbdiLastSuggestion: {},
  cbdoLastSuggestion: {},
  allowCheckin: true,
};

const suggestionSlice = createSlice({
  name: 'suggestions',
  initialState,
  reducers: {
    setSuggestions: (state, action) => {
      state.suggestions = action.payload;
    },
    setStreakDates: (state, action) => {
      state.streakDates = action.payload;
    },
    setCheckinStats: (state, action) => {
      state.checkinStats = action.payload;
    },
    setLastSuggestion: (state, action) => {
      state.lastSuggestion = action.payload;
    },
    setTHCILastSuggestion: (state, action) => {
      state.thciLastSuggestion = action.payload;
    },
    setTHCOLastSuggestion: (state, action) => {
      state.thcoLastSuggestion = action.payload;
    },
    setCBDILastSuggestion: (state, action) => {
      state.cbdiLastSuggestion = action.payload;
    },
    setCBDOLastSuggestion: (state, action) => {
      state.cbdoLastSuggestion = action.payload;
    },
    setAllowCheckin: (state, action) => {
      state.allowCheckin = action.payload;
    },
  },
});

export const {
  setSuggestions,
  setStreakDates,
  setCheckinStats,
  setLastSuggestion,
  setTHCILastSuggestion,
  setTHCOLastSuggestion,
  setCBDILastSuggestion,
  setCBDOLastSuggestion,
  setAllowCheckin,
} = suggestionSlice.actions;
export default suggestionSlice.reducer;
