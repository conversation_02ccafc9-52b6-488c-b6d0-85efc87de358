@use '../variables' as *;

.container {
  display: flex;
  flex: 1;
}

.content {
  display: flex;
  flex-direction: column;
  padding: 0 10px;
  gap: 30px;
}

.subHeading {
  padding: 20px 50px 10px 19px;
}

.header {
  padding: 0 20px;
  gap: 30px;
}

.doseCardsContainer {
  gap: 10px;
  padding: 15px;
}

.subHeadingContainer {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 30px 20px 0 20px;
}

.leftWrapper {
  flex: 1;
  justify-content: flex-start;
}

.subHeading {
  margin-top: 20px;
  padding: 0 20px;
}

.rightWrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  gap: 5px;
}

.homeScreenButton {
  margin: 10px 0;
}

.homeScreenButtonDisabled {
  background-color: $ypd-light-grey;
}

.homeScreenButtonDisabledDark {
  background-color: $ypd-teal;
  border: 1px solid $ypd-text-grey
}
