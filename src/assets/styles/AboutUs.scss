@use '../variables' as *;

.container {
    flex: 1;
    padding: 20px;
}

.heroSection {
    background-color: $ypd-old-green;
    border-radius: 20px;
    padding: 40px;
    margin: 0 0 30px 0;
}

.feelingWrapper {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
}

.gap {
    margin: 0 0 20px 0;
}

.aboutCard {
    border-radius: 25px;
    padding: 35px;
}

.modalContainer {
    flex: 1;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.7);
}

.modalContent {
    background-color: $ypd-modal-background;
    border-radius: 20px;
    padding: 30px;
    align-items: center;
    width: 80%;
}

.gapDown {
    margin: 0 0 20px 0;
}

.box {
    flex-direction: row;
    align-items: center;
    margin: 0 0 20px 0;
    padding: 10px;
    border-radius: 10px;
}

.icon {
    width: 50px;
    height: 50px;
    margin: 0 15px 0 0;
}

.closeButton {
    width: 100%;
    align-items: center;
}

.backButton {
    padding: 20px 0;
}
