@use '../variables' as *;

// Base Profile builder

.profileBuilderContainer {
  flex: 1;
}

.profileBuilderScrollContainer {
  flex-grow: 1;
  padding: 0 20px 0 20px;
}

.progressBar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  padding: 0 20px 0 20px;
}

.profileBuilderButtonContainer {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.toggleContainer {
  margin: 20px 0 0 0;
}

.onlyContinueButtonContainer {
  justify-content: flex-end;
}

.disabledButton {
  background-color: $ypd-white;
  border-width: 1px;
  border-style: solid;
  border-color: $ypd-old-green;
}

.disabledText {
  color: $ypd-black;
}

.bottomButton {
  display: flex;
  margin: 20px 0;
  align-items: flex-end;
  width: 100%;
  padding: 0 10px;
}

.bottomSheetContainer {
  flex: 1;
  margin: 30px 0 0 0;
}

.wrapper {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  gap: 90px;
}

.content {
  gap: 30px;
}

.radioContainer {
  margin: 60px 0 0 0;
}

.radio {
  width: 100%;
  padding: 15px 20px;
  border: 1px solid $ypd-dark-grey;
  border-radius: 33px;
  margin: 5px 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.selectedRadio {
  border: 1px solid $ypd-green;
}

.radioText {
  font-size: 16px;
  text-align: center;
}

.selectedRadioText {
  font-size: 16px;
  text-align: center;
}

.layoutContainer {
  flex: 1;
}

.topGap {
  padding: 60px 0;
}

.topProfileGap {
  padding: 50px 0;
}

.gap {
  padding: 25px 0;
}

.selectedConcernsContainer {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin: 10px 0;
  max-height: 100px;
}

.selectedConcernTag {
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background-color: $ypd-link;
  border-radius: 25px;
  margin: 5px 5px 5px 0;
  padding: 2px 12px 2px 0;
}

.closeButtonIcon {
  margin: 0;
  padding: 0;
}

.healthConcernOption {
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: center;
  padding: 15px 20px;
  border: 1px solid $ypd-dark-grey;
  border-radius: 25px;
  margin: 5px 0;
  width: 100%;
}

.selectedHealthConcern {
  border: 1px solid $ypd-green;
}

.forInputFieldMargin {
  margin: 11px 0 0 0;
  width: 100%;
}

.footerSection {
  align-items: flex-end;
  padding: 10px 10px 15px 20px;
}

.dropDownContainer {
  gap: 15px;
  margin: 20px 0;
}

.strainInput {
  padding: 0 20px 0 20px;
}

.navigationButtonPosition {
  position: absolute;
  top: 560px;
  z-index: 1;
  left: 0;
  right: 20px;
  display: flex;
  flex-direction: row-reverse;
  padding: 0 0 0 20px;
}

.stickyWrapper {
  flex: 1;
  position: relative;
}

.container {
  display: flex;
  flex: 1;
}

.imageContainerProfileBuilder {
  display: flex;
  flex-direction: row;
  justify-content: center;
  width: 100%;
  gap: 10px;
}

.singleItemWrapper {
  width: 175px;
}

.box {
  width: 175px;
  height: 190px;
  border: 1px solid $ypd-dark-grey;
  border-radius: 5px;
  align-items: center;
  justify-content: center;
  background-color: $ypd-white;
}

.productPreferenceImage {
  width: 100px;
  height: 100px;
  border-radius: 10px;
}

.text {
  margin: 15px 0 0 0;
  font-size: 16px;
  font-weight: bold;
}

.textContainer {
  padding: 20px 0;
  gap: 2px;
}

.listItem {
  display: flex;
  flex-direction: row;
  gap: 10px;
}

.cardContainer {
  background-color: $ypd-light-grey;
  padding: 30px 20px;
  gap: 20px;
  border-radius: 10px;
  width: 100%;
}

.listStyleType {
  flex-direction: row;
  align-items: center;
  gap: 5;
}

.imageContainer {
  display: flex;
  align-items: center;
  justify-content: center;
}

.imageJusticeWrapper {
  width: 100px;
  height: 100px;
  margin: 10%;
}

.cardQuestionContainer {
  padding: 30px 20px;
  gap: 20px;
  border-radius: 10px;
}

.smallDescriptionContainer {
  padding: 0 20px;
}

.image {
  width: 200px;
  height: 200px;
  margin: 20px 0 0 0;
}

.headerWrapper {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 2px;
  margin: 20px 0 0 0;
}

.left {
  width: 50%;
  padding: 10px 0;
  display: flex;
  align-items: center;
  background-color: $step5-CBD-heading;
}

.right {
  width: 50%;
  padding: 10px 0;
  display: flex;
  align-items: center;
  background-color: $step5-THC-subheading;
}

.contentWrapper {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.textContainerTHC {
  padding: 15px 20px;
  width: 50%;
}

.textContainerCBD {
  padding: 15px 20px;
  width: 50%;
}

.customBottomButton {
  margin: 20px 20px;
}

.renderProblemItem {
  flex: 1;
  align-items: center;
  padding: 5px;
}

.renderFooterComponent {
  flex-direction: row;
  align-items: center;
  width: 100%;
  justify-content: center;
  margin: 0 0 20px 0;
}
