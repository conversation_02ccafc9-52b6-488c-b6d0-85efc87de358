@use '../variables' as *;

.mainWrapper {
  flex: 1;
  align-items: center;
  justify-content: center;
}

.container {
  padding: 0 20px;
  height: 100%;
  justify-content: space-evenly;
}

.instructionsContiner {
  padding: 30px 0;
}

.buttonContainer {
  align-items: center;
  gap: 10px;
}

.imageContainer {
  align-items: center;
}

.backgroundImage {
  width: 300px;
  height: 310px;
}

.bgImageResponsiveSmall {
  width: 240px;
  height: 250px;
}

.backButton {
  padding: 20px 0 0px 0;
}
