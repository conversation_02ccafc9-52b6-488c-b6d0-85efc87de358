@use '../variables' as *;

.mainWrapper {
    flex: 1;
    padding: 35px 0 0 0;
}

.scrollContainer {
    flex-grow: 1;
    padding: 20px;
}

.mainContainer {
    justify-content: space-evenly;
    flex-grow: 1;
}

.upperContent {
    gap: 25px;
}

.header {
    flex-direction: row;
    justify-content: center;
    margin: 0 0 20px 0;
}

.iconWrapper {
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.gradientCircle {
    width: 80px;
    height: 80px;
    border-radius: 40px;
    align-items: center;
    justify-content: center;
}

.container {
    padding: 20px;
}

.centeredCard {
    width: 100%;
    margin: 0 0 25px 0;
}

.themeCard {
    border-radius: 20px;
    padding: 25px;
    background-color: $ypd-white;
}

.cardContent {
    align-items: center;
    justify-content: center;
}

.themesContainer {
    gap: 25px;
    margin: 0 0 30px 0;
    align-items: center;
}

.backButtonWrapper {
    padding: 5px;
}
