@use '../variables' as *;

.scrollView {
    flex: 1;
}

.mainContainer {
    flex: 1;
}

.calendarContainer {
    background-color: $ypd-white;
}

.calendarShadowContainer{
    box-shadow: 0 2px 4px $ypd-dark-grey;
}

.sideEffectsContainer {
    padding: 20px;
}

.closeIcon {
    align-self: flex-end;
}

.spacer {
    height: 80px;
}

.weekText {
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
}

.backIcon {
    position: absolute;
    left: 11%;
}

.backIconRight {
    position: absolute;
    right: 11%;
}

.buttonContainer {
    flex-direction: row;
    justify-content: space-around;
    margin-bottom: 20px;
}

.buttonWrapper {
    flex-direction: row;
    justify-content: space-around;
    gap: 10px;
    width: 100%;
}

.firstView {
    flex-direction: row;
    align-items: center;
    margin: 0 0 20px 0;
}

.imageContainer {
    align-items: center;
    margin:0 15px 0 0;
}

.textContainer {
    flex: 1;
}

.subHeading {
    margin: 0 0 15px 0;
}

.bottomWrapper {
    align-items: center;
    gap: 20px;
}

.textArea {
    border-color: $ypd-dark-grey;
    margin-bottom: 10px;
    font-size: 14px;
    padding: 10px 0 0 20px;
}

.backButton {
    padding: 20px 0 25px 25px;
}

.profileBuilderScrollContainer {
    flex-grow: 1;
    padding: 0 20px 0 20px;
    background-color: $ypd-white;
}

.spacerX {
    height: 20px;
}
