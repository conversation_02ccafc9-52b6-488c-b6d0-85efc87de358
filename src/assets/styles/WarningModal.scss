@use '../variables' as *;

.overlay {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: $limit-modal-shadow;
  padding: 20px;
  height: 100%;
  width: 100%;
}

.contentContainer {
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 4px $ypd-dark-grey;
  align-self: center;
}

.wrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
}

.textContainer {
  width: 70%;
}

.closeButton {
  position: absolute;
  top: -25px;
  right: -25px;
  border-radius: 20px;
  box-shadow: 0 2px 2px $ypd-dark-grey;
  z-index: 10;
}
