@use '../variables' as *;

.container {
    flex: 1;
}

.header {
    padding: 30px 25px;
}

.headerContent {
    align-items: center;
}

.iconContainer {
    width: 65px;
    height: 65px;
    border-radius: 35px;
    background-color: $ypd-green;
    justify-content: center;
    align-items: center;
    margin-bottom: 16px;
}

.headerSubtitle {
    padding-top: 20px;
    opacity: 0.8;
}

.scrollContainer {
    flex: 1;
}

.scrollContent {
    padding-bottom: 40px;
}

.mainCard {
    background-color: $ypd-white;
    border-radius: 20px;
    padding: 15px 25px;
    margin: 0 0 10px 0;
    width: 95%;
    align-self: center;
}

.switchRow {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}

.switchInfo {
    flex: 1;
}

.mainTitle {
    margin-bottom: 4px;
}

.timeSection {
    padding-top: 20px;
    align-items: center;
    gap: 20px;
}

.sectionHeader {
    margin-bottom: 20px;
    text-align: center;
}

.timeCard {
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
    border:1px;
    border-width: 1.5px;
}

.timeRow {
    flex-direction: row;
    align-items: center;
    gap: 20px;
}

.timeInfo {
    flex-direction: row;
    align-items: center;
    justify-content: center;
}

.timeIconContainer {
    width: 48px;
    height: 48px;
    border-radius: 24px;
    background-color: $ypd-light-grey;
    justify-content: center;
    align-items: center;
    margin-right: 10px;
}

.timeButton {
    border-radius: 20px;
}

.warningText {
    padding: 35px;
}

.modalOverlay {
    flex: 1;
    background-color: rgba(0, 0, 0, 0.5);
    justify-content: flex-end;
}

.modalContent {
    border-radius: 20px 20px 0 0;
    justify-content: center;
    align-items: center;
    padding: 25px;
}

.modalHeader {
    align-items: center;
    margin-bottom: 10px;
}

.modalIconContainer {
    width: 64px;
    height: 64px;
    border-radius: 32px;
    background-color: $ypd-light-grey;
    justify-content: center;
    align-items: center;
    margin-bottom: 15px;
}

.modalMessage {
    margin-bottom: 30px;
}

.modalActions {
    gap: 1px;
}

.modalButton {
    border-width: 2px;
    border-color: $ypd-old-green;
}

.backButton {
    padding: 25px;
}

.gradientCircle {
    width: 80px;
    height: 80px;
    border-radius: 40px;
    align-items: center;
    justify-content: center;
}
