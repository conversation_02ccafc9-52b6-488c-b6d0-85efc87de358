@use '../../assets/variables' as *;

.container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 30px;
}

.contentWrapper {
  justify-content: space-between;
  gap: 50px;
}

.scrollViewContent {
  flex-grow: 1;
  justify-content: center;
  align-items: center;
  gap: 50px;
}

.textContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.imageContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  max-height: 200px;
}

.description {
  font-size: 16px;
  color: $ypd-text;
  text-align: center;
  line-height: 24px;
  width: 315px;
}

.bottomContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
}

.dotContainer {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 48px;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 4px;
  margin: 0 4px;
}

.loginText {
  color: $ypd-text;
  font-size: 14px;
  text-align: center;
  align-self: center;
}

.logInHighlight {
  color: $ypd-link;
  text-decoration: none;
}

.bottomLoginButton {
  padding: 0 0 20px 0;
}
