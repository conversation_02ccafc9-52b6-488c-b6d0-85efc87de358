@use '../variables' as *;

.fullScreenContainer {
  display: flex;
  flex: 1;
  position: relative;
}

.centeredContent {
  display: flex;
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.iconContainer {
  margin: 0 0 20px 0;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 127px;
  height: 113px;
}

.icon {
  width: 127px;
  height: 113px;
  object-fit: contain;
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  top: 0;
  left: 0;
}

.title {
  font-size: 25px;
  margin: 0 0 20px 0;
  line-height: 45px;
  padding: 0 20px;
  text-align: center;
}

.subtext {
  font-size: 16px;
  margin: 10px 0 0 0;
  padding: 0 20px;
  text-align: center;
}
