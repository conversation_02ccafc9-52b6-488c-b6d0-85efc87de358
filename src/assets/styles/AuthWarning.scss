@use '../variables' as *;

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: $ypd-point4-black;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.contentContainer {
  border-radius: 10px;
  padding: 20px;
  align-self: center;
}

.wrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
}

.textContainer {
  width: 70%;
}

.closeButton {
  position: absolute;
  top: -25px;
  right: -25px;
  border-radius: 20px;
  z-index: 10;
}
