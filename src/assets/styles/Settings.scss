@use '../variables' as *;

.safeAreaView {
  flex: 1;
}

.screen {
  flex: 1;
  padding-top: 20px;
}

.headerContainer {
  padding: 0 0 5px 28px;
}

.myProfile {
  padding: 0 0 0 8px;
}

.buttonContainer {
  flex: 1;
  padding: 20px;
}

.divider {
  height: 1px;
  background-color: $ypd-dark-grey;
  margin: 15px 0px 15px 0px;
  width: 97%;
}

.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  align-items: center;
  justify-content: center;
  z-index: 999;
}
