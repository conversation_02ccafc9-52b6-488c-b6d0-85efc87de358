@use '../variables' as *;

.mainWrapper {
  flex: 1;
  padding: 35px 0;
}

.innerContainer {
  padding: 20px 30px 10px;
  background-color: transparent;
}

.cardContent {
  flex-direction: row;
  align-items: center;
}

.buttonContainer {
  margin: 25px 0 0 0;
  flex-direction: row;
  justify-content: center;
}

.message {
  text-align: center;
  margin: 7px 0 20px 0;
  font-size: 12px;
}

.card {
  margin: 8px 0;
  padding: 15px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.shadowLine {
  height: 1px;
  opacity: 0.1;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.2);
}


.filename {
  margin: 0 0 0 10px;
}

.bottomButtonContainer {
  padding: 0 35px 15px;
}

.emptyStateWrapper {
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.emptyStateTitle {
  font-size: 15px;
  font-weight: bold;
  margin: 0 0 8px 0;
}

.emptyStateDescription {
  font-size: 14px;
  text-align: center;
  margin: 0 0 20px 0;
}
