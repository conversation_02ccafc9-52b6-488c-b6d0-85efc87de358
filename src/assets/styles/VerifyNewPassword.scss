@use '../variables' as *;

.container {
  flex: 1;
  display: flex;
  background-color: $ypd-white;
  justify-content: center;
  padding: 40px 20px;
}

.heading {
  margin: 30px 0 50px 0;
}

.headingSmall {
  margin: 30px 0;
}

.wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 20px;
  width: 100%;
}

.textWrapper {
  display: flex;
  flex-direction: row;
  gap: 10px;
}

.inputHeading {
  padding: 10px 0 10px 20px;
}

.resendCode {
  padding: 10px 0;
}

.forgotPassword {
  color: $ypd-link;
  margin: 5px 0 30px 0;
  padding: 0 15px;
  font-size: 12px;
}
