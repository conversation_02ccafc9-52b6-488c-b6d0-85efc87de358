@use '../variables' as *;

.container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
}

.overlay {
  display: flex;
  background-color: $ypd-point4-black;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
  align-items: center;
  justify-content: flex-end;
}

.modalContent {
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  padding: 20px 20px 0 20px;
  width: 100%;
}

.closeIcon {
  display: flex;
  align-items:flex-end;
}

.title {
  margin: 20px 0;
}

.dailyWeeklyContainer {
  display: flex;
  flex-direction: row;
  margin: 10px 0;
  justify-content: space-around;
}

.dailyWeekly {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.cardContainer {
  background-color: $ypd-light-green;
  padding: 20px 20px;
  gap: 20px;
  margin: 30px 0;
  border-radius: 10px;
  border: 1px solid $ypd-old-green;
}
