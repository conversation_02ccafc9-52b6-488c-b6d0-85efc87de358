@use '../variables' as *;

.container {
    flex: 1;
}

.mainContainer {
    flex: 1;
    justify-content: space-evenly;
}

.content {
    padding: 20px;
}

.iconCard {
    align-items: center;
    margin: 0 0 20px 0;
}

.gradientCircle {
    width: 65px;
    height: 65px;
    border-radius: 30px;
    justify-content: center;
    align-items: center;
    margin: 0 0 15px 0;
}

.inputGroup {
    margin: 0 0 20px 0;
    align-items: center;
}

.gap {
    margin: 0 0 20px 0;
}

.labelsGap {
    margin: 0 0 10px 0;
}

.optionRow {
    flex-direction: row;
    gap: 20px;
    margin: 0 0 20px 0;
    flex-wrap: wrap;
}

.optionButton {
    padding: 10px 20px;
    border-radius: 20px;
    border-width: 1px;
    border-color: $ypd-border-light;
}

.selectedOption {
    background-color: $ypd-green;
    border-color: $ypd-green;
}

.optionText {
    color: $ypd-text;
}

.selectedOptionText {
    color: $ypd-white;
}

.buttonContainer {
    margin: 20px 0 0 0;
    gap: 20px;
}

.backButton {
    padding: 0 20px;
}
