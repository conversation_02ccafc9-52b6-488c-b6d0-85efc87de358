@use '../variables' as *;

.container {
  display: flex;
  gap: 20px;
}

.divider {
  height: 2px;
  background-color: $ypd-blue;
  width: 100%;
}

.wrapper {
  display: flex;
  flex-direction: column;
}

.buttonRow {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px 0;
}

.textContainer {
  align-self: center;
}

.gap {
  height: 20px;
}

.overlay {
  flex: 1;
  background-color: $ypd-white;
  justify-content: center;
  opacity: 0.99;
  align-items: center;
}

.closeButton {
  position: absolute;
  right: 0;
  padding: 5px;
}

.modalTitle {
  padding: 20px;
}

.doseValue {
  margin: 0 20px 0 0;
}

.rowContainer {
  flex-direction: row;
  align-items: center;
  gap: 3px;
}

.customDisabledButton {
  border-color: $stroke-icon-color;
  background-color: $stroke-icon-color;
}

.customTealButton {
  border-color: $ypd-teal;
  background-color: $ypd-teal;
}

.customGreenButton {
  border-color: $ypd-green;
  background-color: $ypd-green;
}

.customTimerButtonUp {
  border-color: $timer-color-blue;
  background-color: $timer-color-blue;
}

.customTimerButtonDown {
  border-color: $timer-color-green;
  background-color: $timer-color-green;
}

.customTimerButton {
   border-color: $ypd-blue;
   background-color: $ypd-blue;
}

.customDisabledText {
  color: $ypd-black;
}

.doseCardsContainer {
  gap: 10px;
  padding: 15px;
  background-color: $ypd-white;
}
