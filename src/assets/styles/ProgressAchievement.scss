@use '../variables' as * ;

.container {
  background-color: $ypd-white;
  flex: 1;
}

.toggleContainer {
  width: 100%;
  height: 120px;
  background-color: $ypd-light-grey;
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  padding: 0 16px;
  gap: 12px;
}

.borderButtons {
  border-bottom: 3px solid $ypd-green;
}

.dailyWeeklyContainer {
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
}

.dailyWeekly {
  display: flex;
  align-items: center;
  width: 30%;
}

.horizontalDivider {
  width: 100%;
  height: 9px;
  margin: 20px 0 0 0;
}

.weeklyCalendarView {
  display: flex;
  justify-content: center;
  align-items: center;
}
