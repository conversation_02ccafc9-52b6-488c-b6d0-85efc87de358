@use '../variables' as *;

.mainWrapper {
    flex: 1;
    padding: 35px 0 0 0;
}

.scrollContainer {
    flex-grow: 1;
}

.container {
    padding: 20px;
}

.header {
    flex-direction: row;
    margin-bottom: 30px;
}

.backButton {
    padding: 5px;
}

.centeredCard {
    align-self: center;
    width: 100%;
    margin-bottom: 15px;
}

.themesContainer {
    gap: 25px;
    margin-bottom: 30px;
}

.shadowContainer {
    border-radius: 15px;
    margin-bottom: 2px;
}

.themeCard {
    border-radius: 15px;
    padding: 20px;
}

.selectedCard {
    border-width: 2px;
    border-color: $ypd-green;
}

.cardContent {
    flex-direction: row;
    align-items: center;
}

.phonePreview {
    margin-right: 15px;
    border-radius: 12px;
    width: 60px;
    height: 100px;
}

.lightPhonePreview {
    background-color: $ypd-green;
}

.darkPhonePreview {
    background-color: $ypd-dark-teal;
}

.phoneIcon {
    flex: 1;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.themeInfo {
    flex: 1;
}

.checkmark {
    margin-left: 10px;
}

.systemDefaultContainer {
    flex-direction: row;
    justify-content: space-between;
    border-radius: 19px;
    padding: 15px 20px;
    border-color: $ypd-old-green;
}

.bottomPadding {
    height: 25px;
}

.iconWrapper {
    flex: 1;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.gradientCircle {
    width: 80px;
    height: 80px;
    border-radius: 40px;
    align-items: center;
    justify-content: center;
}
