@use '../variables' as *;

.safeArea {
  flex: 1;
}

.container {
  flex: 1;
}

.toggleContainer {
  height: 70px;
  display: flex;
  flex-direction: row;
  padding: 0 25px;
  gap: 25px;
}

.weeklyCalendarView {
  margin: 5px 0 10px 0;
}

.dateHeader {
  align-items: start;
  padding: 0 0 10px 45px;
}

.dateText {
  font-size: 24px;
  color: $ypd-black;
}

.timelineContainer {
  flex: 1;
  padding: 20px 20px 40px 20px;
}

.timelineColumn {
  position: absolute;
  left: 30px;
  flex-direction: column;
  align-items: center;
  height: 105%;
  padding: 30px 0 0 0;
}

.dot {
  width: 15px;
  height: 15px;
  border-radius: 10px;
  background-color: $ypd-black;
}

.verticalLine {
  width: 2px;
  flex: 1;
  background-color: $ypd-black;
}

.timelineRow {
  flex-direction: row;
  margin: 0 0 15px 18px;
}

.cardWithConnector {
  flex-direction: row;
  align-items: center;
}

.horizontalLine {
  width: 20px;
  height: 2px;
  background-color: $ypd-black;
}

.card {
  flex-direction: row;
  border-radius: 12px;
  overflow: hidden;
}

.cardContent {
  flex: 1;
  padding: 10px;
}

.cardTopRow,
.cardBottomRow {
  flex-direction: row;
  justify-content: space-between;
  margin: 0 0 10px 0;
}

.cardTextRow {
  flex-direction: row;
  align-items: center;
}

.howIFeltRow {
  margin-top: 15px;
}

.cardRightBorder {
  width: 5px;
}

.noActivitiesContainer {
  margin: 15px 0 0 0;
  align-items: center;
  justify-content: center;
}

.borderButtons {
  border-bottom-width: 4px;
  border-bottom-color: $ypd-green;
  border-radius: 0;
}

.calenderContainer {
  flex: 1;
}
