@use '../variables' as *;

.wrapper {
  width: 100%;
  padding: 10px 0;
}

.headerContainer {
  width: 100%;
  align-items: center;
}

.container {
  width: 100%;
  padding: 0 20px;
}

.headingText {
  margin-top: 5px;
  margin-bottom: 10px;
  justify-content: start;
  padding: 0 20px;
}

.circularSliderWrapper {
  align-self: center;
  margin: 20px 0 0 0;
}

.svgWrapper {
  align-items: center;
  justify-content: center;
}

.centerImageWrapper {
  position: absolute;
  align-items: center;
  justify-content: center;
}
