@use '../variables' as *;

.container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.modalContainer {
    display: flex;
    justify-content: flex-end;
    background-color: $ypd-half-black;
    height: 100%;
}

.modalContent {
    background-color: $ypd-white;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    padding: 20px;
    min-height: 350px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.modalHeader {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}

.sliderContainer {
    width: 90%;
    margin-left: auto;
    margin-right: auto;
}

.valueBubbleContainer {
    position: absolute;
    top: -50px;
}

.valueBubble {
    background-color: $ypd-green;
    padding: 4px 10px;
    border-radius: 6px;
}

.slider {
    width: 100%;
    height: 40px;
}

.customTrackContainer {
    display: flex;
    flex-direction: row;
    height: 15px;
    border-radius: 8px;
    width: 100%;
    position: absolute;
    top: 12px;
    background-color: $ypd-olive-green;
}

.trackGradient {
    height: 15px;
    border-radius: 8px;
}

.emptyTrack {
    height: 15px;
    background-color: $ypd-mid-grey;
    border-radius: 8px;
}

.track {
    height: 15px;
    border-radius: 8px;
    background-color: transparent;
}

.thumb {
    width: 10px;
    height: 45px;
    border-radius: 8px;
    background-color: $ypd-green;
}

.rangeLabels {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin: 5px 0 0 0;
}
