@use '../variables' as *;

.cardWrapper {
  flex-direction: row;
  gap: 20px;
  margin: 0 0 0 30px;
}

.card {
  width: 215px;
  height: 260px;
  background-color: $ypd-white;
  border-radius: 10px;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 50px 0 3px 0;
}

.headWrapper {
  display: flex;
  align-items: center;
}

.cardContent {
  margin: 15px 0 0 0;
}

.divider {
  width: 80%;
  height: 1px;
  background-color: $ypd-dark-grey;
  margin: 15px;
}

.buttonContainer {
  flex-direction: row;
  justify-content: space-around;
  width: 100%;
  margin: 0 0 20px 0;
}
