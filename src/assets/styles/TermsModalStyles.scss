@use '../variables' as *;

.mainContainer {
  flex: 1;
}

.modalContent {
  flex-grow: 1;
  display: flex;
  justify-content: space-evenly;
  padding: 30px 10px 0 10px;
}

.modalHeader {
  padding: 0 20px;
}

.modalDescription {
  font-size: 16px;
}

.inlineTextContainer {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: baseline;
}

.conditionContainer {
  display: flex;
  flex-direction: row;
  padding: 0 15px;
  gap: 10px;
  margin: 0 0 5px 0;
}

.bullet {
  font-size: 20px;
  line-height: 25px;
}

.termsWrapper {
  padding: 0 20px;
}

.link {
  color: $ypd-link;
  text-decoration: none;
  text-decoration-color: $ypd-link;
}

.agreeSectionWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

.agreeSectionInsideWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80%;
  align-self: center;
}

.checkboxContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
  margin: 20px 0;
  padding: 0 20px;
}

.checkbox {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 10px 0 0;
  background-color:$ypd-mid-grey;
}

.checkboxChecked {
  background-color: $ypd-link;
  border-color: $ypd-link;
}

.checkboxText {
  font-size: 14px;
  flex-shrink: 1;
}

.divider {
  height: 1px;
  background-color: $ypd-dark-grey;
  margin: 15px 0 15px 0px;
  width: 100%;
}
