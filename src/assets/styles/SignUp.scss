@use '../variables' as *;

.safeArea {
  display: flex;
  flex: 1;
}

.container {
  display: flex;
  flex: 1;
  padding: 0 20px;
}

.heading {
  padding: 80px 0;
}

.headingSmall {
  margin: 30px 0;
}

.inputHeading {
  padding: 10px 15px;
}

.forgotPassword {
  color: $ypd-link;
  text-decoration: none;
  padding: 3px 15px 0 15px;
  text-decoration-color: $ypd-link;
}

.passwordCheck {
  color: $ypd-link;
  padding: 3px 15px 0 15px;
}

.buttonContainer {
  margin: 17px 0;
}

.dividerContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 30px 0;
}

.divider {
  flex: 1;
  height: 1px;
  background-color: $ypd-text-grey;
}

.dividerText {
  padding: 0 16px;
}

.googleButton {
  margin: 0 0 10px 0;
}
