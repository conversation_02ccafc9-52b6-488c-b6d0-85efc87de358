@use '../variables' as *;

.safetyModalContainer {
  flex: 1;
  padding: 0 30px;
  justify-content: center;
}

.conditionContainer {
  gap: 7px;
  margin: 5px 0 0 0;
}

.buttonWrapper {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin: 45px 0 0 0;
}

.buttonWrapperSmall {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin: 10px 0 0 0;
}

.headingStyle {
  padding: 25px 0 25px 0;
}

.container {
  flex: 1;
  padding: 20px;
  justify-content: center;
  gap: 50px;
}

.card {
  background-color: $ypd-white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 4px $ypd-dark-grey;
}

.buttonContainer {
  flex-direction: row;
  justify-content: space-around;
}

.button {
  box-shadow: 0 2px 4px $ypd-dark-grey;
}

.modalContainer {
  justify-content: center;
  align-items: center;
  box-shadow: 1px 2px 4px $ypd-dark-grey;
  padding: 20px;
  border-radius: 16px;
}

.modalContent {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  gap: 10px;
  border-radius: 2px;
}

.modalHeader {
  margin-bottom: 15px;
}

.mainContainer {
  flex: 1;
  padding: 20px;
  justify-content: center;
  gap: 50px;
  background-color: $ypd-half-black;
}

.modalText {
  margin: 0 0 15px 0;
  line-height: 24px;
}

.bottomButton {
  align-items: center;
}
