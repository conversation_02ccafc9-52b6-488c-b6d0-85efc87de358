@use '../variables' as *;

.header {
  gap: 35px;
  padding: 0 20px;
}

.container {
  flex: 1;
  display: flex;
  gap: 20px;
  padding: 50px 20px 0;
}

.headingContainer {
  padding-right: 5px;
  padding-top: 10px;
}

.symptomsContainer {
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 10px;
}

.blockContainer {
  width: 48%;
  height: 66px;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  border: 1px solid $ypd-dark-grey;
}

.highlightedBlock {
  background-color: $ypd-light-green;
  border: 1px solid $ypd-green;
}

.nonHighlightedBlock {
  background-color: transparent;
  border: 1px solid $ypd-green;

}

.sideEffectContainer {
  display: flex;
  flex: 1;
  justify-content: flex-end;
  padding: 20px 20px 35px 20px;
}
