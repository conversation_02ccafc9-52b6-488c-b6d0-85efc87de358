@use '../variables' as *;

.heading {
  padding: 0 20px 10px 20px;
}

.dropdown {
  padding: 12px;
  border-width: 1px;
  border-color: $ypd-dark-grey;
  border-radius: 30px;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.placeholderStyle {
  font-size: 16px;
  color: $ypd-black;
  margin:0 0 0 20px;
}

.arrow {
  font-size: 16px;
  color: $ypd-mid-grey;
  margin: 0 0 0 10px;
}

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: $limit-modal-shadow;
}

.closeButton {
  position: absolute;
  top: 5px;
  right: 5px;
  padding: 5px;
}

.modalContent {
  width: 80%;
  height: 50%;
  padding: 25px;
  border-radius: 10px;
}

.dropdownList {
  margin: 10px 0 0 0;
}

.dropdownItem {
  padding: 10px 15px;
  border-bottom: 1px solid $ypd-mid-grey;
}

.separator {
  height: 1px;
  margin: 5px 0;
  background-color: $ypd-mid-grey;
  width: 100%;
}
