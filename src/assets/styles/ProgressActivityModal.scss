@use '../variables' as *;

.centeredView {
    flex: 1;
    justify-content: center;
    align-items: center;
    background-color: $ypd-point4-black;
}

.modalView {
    border-radius: 10px;
    padding: 25px 10px 10px 20px;
    width: 85%;
}

.textContainer {
    flex-direction: row;
    flex-wrap: wrap;
}

.modalValue {
    opacity: 0.7;
    flex-shrink: 1;
    margin-bottom: 5px;
}

.closeIcon {
    position: absolute;
    right: 5px;
}

.scrollContainer {
    padding-right: 10px;
    max-height: 200px;
}