@use '../variables' as *;

.container {
  flex: 1;
}

.row {
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
}

.iconContainer {
  flex-direction: row;
  align-items: center;
}

.iconWrapper {
  background-color: $ypd-light-grey;
  border-radius: 100px;
  padding: 10px;
  margin: 0 0 0 10px;
  justify-content: center;
  align-items: center;
}

.shadowContainer {
  padding: 15px;
}

.cardsBorderRadius{
  border-radius: 6px;
  overflow: hidden;

}

.card {
  padding: 20px;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  border-radius: 6px;
}

.streakContainer {
  flex-direction: row;
  align-items: center;
}

.streakTextContainer {
  margin: 0 0 0 10px;
}

.checkButton {
  background-color: $ypd-blue;
  align-items: center;
  justify-content: center;
}
