@use '../variables' as *;

.fullscreen-modal-container {
    display: flex;
    flex: 1;
    position: relative;
}

.modal-content {
    display: flex;
    flex: 1;
}

.centered-content {
    display: flex;
    flex: 1;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.close-button-text {
    font-size: 32px;
    color: $ypd-light-black;
}

.modal-icon-container {
    margin: 0 0 20px 0;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 127px;
    height: 113px;
}

.modal-icon {
    width: 127px;
    height: 113px;
    object-fit: contain;
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 0;
    left: 0;
}

.triangle {
    width: 0;
    height: 0;
    border-left: 63.5px solid transparent;
    border-right: 63.5px solid transparent;
    border-bottom: 113px solid $ypd-old-green;
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-63.5px);
}

.modal-title {
    font-size: 25px;
    margin: 0 0 20px 0;
    color: $ypd-light-black;
    line-height: 45px;
    padding: 0 20px;
    text-align: center;
}

.modal-subtext {
    font-size: 16px;
    margin: 10px 0 0 0;
    padding: 0 20px;
    text-align: center;
}
