import * as React from 'react';
import Svg, { Rect, Path } from 'react-native-svg';
const Thunder = (props) => (
  <Svg
    width={65}
    height={65}
    viewBox="0 0 70 70"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <Rect width={70} height={70} rx={35} fill="#0BDDB4" />
    <Path
      d="M24.7683 56C24.6207 56 24.4723 55.9574 24.3426 55.8708C24.0477 55.6738 23.9253 55.3001 24.0463 54.9662L30.8519 36.1769H24.7685C24.4513 36.1769 24.1667 35.9814 24.052 35.6847C23.9374 35.388 24.0163 35.0512 24.2507 34.8368L45.7138 15.2013C45.9752 14.962 46.3661 14.9329 46.66 15.1309C46.9539 15.3289 47.0748 15.7026 46.953 16.036L40.1518 34.6355H46.2316C46.5482 34.6355 46.8322 34.8302 46.9473 35.126C47.0625 35.4217 46.9849 35.758 46.7521 35.9731L25.289 55.7962C25.1431 55.931 24.9562 56 24.7683 56ZM26.7511 34.6355H31.9487C32.1993 34.6355 32.4341 34.7582 32.5779 34.964C32.7217 35.1699 32.7564 35.4331 32.6709 35.6693L26.5672 52.5203L44.2628 36.1769H39.0515C38.8005 36.1769 38.5653 36.0539 38.4216 35.8475C38.2778 35.641 38.2437 35.3772 38.3301 35.1409L44.4285 18.4634L26.7511 34.6355Z"
      fill="white"
      stroke="white"
    />
  </Svg>
);
export default Thunder;
