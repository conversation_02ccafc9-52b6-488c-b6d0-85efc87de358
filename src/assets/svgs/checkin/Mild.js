import * as React from 'react';
import Svg, { Circle, Path, Rect } from 'react-native-svg';
const Mild = (props) => (
  <Svg
    width={36}
    height={36}
    viewBox="0 0 36 36"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <Circle cx={18.0007} cy={18.0005} r={14.5161} fill="white" />
    <Path
      d="M18 0C8.01026 0 0 8.1 0 18C0 27.9 8.01026 36 18 36C27.9897 36 36 27.9 36 18C36 8.1 27.9001 0 18 0ZM18 32.4C10.0802 32.4 3.59965 25.9197 3.59965 18C3.59965 10.0802 10.0803 3.6 18 3.6C25.9197 3.6 32.4003 10.0802 32.4003 18C32.4003 25.9198 25.9198 32.4 18 32.4ZM24.3 16.2C25.8303 16.2 27.0001 15.0302 27.0001 13.5C27.0001 11.9698 25.8302 10.8 24.3 10.8C22.7698 10.8 21.6 11.9698 21.6 13.5C21.6 15.0302 22.7698 16.2 24.3 16.2ZM11.7 16.2C13.2302 16.2 14.4 15.0302 14.4 13.5C14.4 11.9698 13.2301 10.8 11.7 10.8C10.1697 10.8 8.99991 11.9698 8.99991 13.5C8.99991 15.0302 10.1698 16.2 11.7 16.2Z"
      fill="#0AB49C"
    />
    <Rect x={10} y={20} width={16} height={3} rx={1.5} fill="#0AB49C" />
  </Svg>
);
export default Mild;
