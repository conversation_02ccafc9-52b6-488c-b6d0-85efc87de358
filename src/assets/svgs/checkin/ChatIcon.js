import * as React from 'react';
import Svg, { Path } from 'react-native-svg';
import { View } from 'react-native';
import { ThemeContext } from '../../../context/ThemeContext';
const ChatIcon = (props) => {
  const { theme } = React.useContext(ThemeContext);
  return (
    <View
      style={{
        backgroundColor: theme.colors.svgColor,
        borderRadius: 20,
        width: 30,
        height: 30,
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <Svg
        width={18}
        height={18}
        viewBox="0 0 21 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <Path
          d="M9.02819 9H15.0282C15.2934 9 15.5478 8.89464 15.7353 8.70711C15.9228 8.51957 16.0282 8.26522 16.0282 8C16.0282 7.73478 15.9228 7.48043 15.7353 7.29289C15.5478 7.10536 15.2934 7 15.0282 7H9.02819C8.76297 7 8.50862 7.10536 8.32108 7.29289C8.13354 7.48043 8.02819 7.73478 8.02819 8C8.02819 8.26522 8.13354 8.51957 8.32108 8.70711C8.50862 8.89464 8.76297 9 9.02819 9ZM5.02819 7C4.83041 7 4.63707 7.05865 4.47262 7.16853C4.30817 7.27841 4.17999 7.43459 4.10431 7.61732C4.02862 7.80004 4.00882 8.00111 4.0474 8.19509C4.08599 8.38907 4.18123 8.56725 4.32108 8.70711C4.46093 8.84696 4.63912 8.9422 4.8331 8.98079C5.02708 9.01937 5.22814 8.99957 5.41087 8.92388C5.5936 8.84819 5.74977 8.72002 5.85966 8.55557C5.96954 8.39112 6.02819 8.19778 6.02819 8C6.02819 7.73478 5.92283 7.48043 5.73529 7.29289C5.54776 7.10536 5.2934 7 5.02819 7ZM5.02819 11C4.83041 11 4.63707 11.0586 4.47262 11.1685C4.30817 11.2784 4.17999 11.4346 4.10431 11.6173C4.02862 11.8 4.00882 12.0011 4.0474 12.1951C4.08599 12.3891 4.18123 12.5673 4.32108 12.7071C4.46093 12.847 4.63912 12.9422 4.8331 12.9808C5.02708 13.0194 5.22814 12.9996 5.41087 12.9239C5.5936 12.8482 5.74977 12.72 5.85966 12.5556C5.96954 12.3911 6.02819 12.1978 6.02819 12C6.02819 11.7348 5.92283 11.4804 5.73529 11.2929C5.54776 11.1054 5.2934 11 5.02819 11ZM10.0282 0C8.71497 0 7.41461 0.258658 6.20135 0.761205C4.9881 1.26375 3.8857 2.00035 2.95712 2.92893C1.08175 4.8043 0.0281864 7.34784 0.0281864 10C0.0194442 12.3091 0.818979 14.5485 2.28819 16.33L0.288186 18.33C0.149429 18.4706 0.0554325 18.6492 0.0180584 18.8432C-0.0193158 19.0372 0.0016069 19.2379 0.0781863 19.42C0.161244 19.5999 0.29589 19.7511 0.465033 19.8544C0.634176 19.9577 0.830187 20.0083 1.02819 20H10.0282C12.6804 20 15.2239 18.9464 17.0993 17.0711C18.9746 15.1957 20.0282 12.6522 20.0282 10C20.0282 7.34784 18.9746 4.8043 17.0993 2.92893C15.2239 1.05357 12.6804 0 10.0282 0ZM10.0282 18H3.43819L4.36819 17.07C4.46267 16.9774 4.53784 16.8669 4.58934 16.7451C4.64084 16.6232 4.66764 16.4923 4.66819 16.36C4.66443 16.0962 4.5566 15.8446 4.36819 15.66C3.05877 14.352 2.24336 12.6305 2.06088 10.7888C1.87839 8.94705 2.34013 7.09901 3.36741 5.55952C4.3947 4.02004 5.92398 2.88436 7.6947 2.34597C9.46543 1.80759 11.368 1.8998 13.0784 2.60691C14.7888 3.31402 16.201 4.59227 17.0746 6.22389C17.9482 7.85551 18.2291 9.73954 17.8693 11.555C17.5096 13.3705 16.5315 15.005 15.1017 16.1802C13.672 17.3554 11.8789 17.9985 10.0282 18ZM15.0282 11H9.02819C8.76297 11 8.50862 11.1054 8.32108 11.2929C8.13354 11.4804 8.02819 11.7348 8.02819 12C8.02819 12.2652 8.13354 12.5196 8.32108 12.7071C8.50862 12.8946 8.76297 13 9.02819 13H15.0282C15.2934 13 15.5478 12.8946 15.7353 12.7071C15.9228 12.5196 16.0282 12.2652 16.0282 12C16.0282 11.7348 15.9228 11.4804 15.7353 11.2929C15.5478 11.1054 15.2934 11 15.0282 11Z"
          fill={theme.colors.svgGear}
        />
      </Svg>
    </View>
  );
};

export default ChatIcon;
