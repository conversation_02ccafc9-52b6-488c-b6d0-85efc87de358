import * as React from 'react';
import { View } from 'react-native';
import Svg, { Path } from 'react-native-svg';
import { ypdLightGrey } from '../../../utils/colors';

const SideStats = (props) => (
  <View
    style={{
      backgroundColor: ypdLightGrey,
      borderRadius: 20,
      padding: 5,
      width: 30,
      height: 30,
    }}
  >
    <Svg
      width={20}
      height={20}
      viewBox="0 0 25 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        d="M18.1667 7.90909L20.2917 7.90909C20.4795 7.90909 20.6597 7.83247 20.7925 7.69608C20.9254 7.55969 21 7.3747 21 7.18182C21 6.98893 20.9254 6.80395 20.7925 6.66756C20.6597 6.53117 20.4795 6.45455 20.2917 6.45455L18.1667 6.45455C18.1667 6.06878 18.0174 5.69881 17.7517 5.42603C17.4861 5.15325 17.1257 5 16.75 5L15.3333 5C14.9576 5 14.5973 5.15325 14.3316 5.42603C14.0659 5.69881 13.9167 6.06877 13.9167 6.45454L4.70833 6.45454C4.52047 6.45454 4.34031 6.53117 4.20747 6.66756C4.07463 6.80395 4 6.98893 4 7.18182C4 7.3747 4.07463 7.55969 4.20747 7.69608C4.34031 7.83247 4.52047 7.90909 4.70833 7.90909L13.9167 7.90909C13.9167 8.29486 14.0659 8.66483 14.3316 8.93761C14.5973 9.21039 14.9576 9.36364 15.3333 9.36364L16.75 9.36364C17.1257 9.36364 17.4861 9.21039 17.7517 8.93761C18.0174 8.66483 18.1667 8.29486 18.1667 7.90909ZM16.75 6.45455L16.75 7.90909L15.3333 7.90909L15.3333 6.45454L16.75 6.45455ZM11.0833 13.7273L20.2917 13.7273C20.4795 13.7273 20.6597 13.6507 20.7925 13.5143C20.9254 13.3779 21 13.1929 21 13C21 12.8071 20.9254 12.6221 20.7925 12.4857C20.6597 12.3494 20.4795 12.2727 20.2917 12.2727L11.0833 12.2727C11.0833 11.887 10.9341 11.517 10.6684 11.2442C10.4027 10.9714 10.0424 10.8182 9.66667 10.8182L8.25 10.8182C7.87428 10.8182 7.51394 10.9714 7.24827 11.2442C6.98259 11.517 6.83333 11.887 6.83333 12.2727L4.70833 12.2727C4.52047 12.2727 4.34031 12.3493 4.20747 12.4857C4.07463 12.6221 4 12.8071 4 13C4 13.1929 4.07463 13.3779 4.20747 13.5143C4.34031 13.6506 4.52047 13.7273 4.70833 13.7273L6.83333 13.7273C6.83333 14.113 6.98259 14.483 7.24827 14.7558C7.51394 15.0286 7.87428 15.1818 8.25 15.1818L9.66667 15.1818C10.0424 15.1818 10.4027 15.0286 10.6684 14.7558C10.9341 14.483 11.0833 14.113 11.0833 13.7273ZM9.66667 12.2727L9.66667 13.7273L8.25 13.7273L8.25 12.2727L9.66667 12.2727ZM15.3333 19.5455L20.2917 19.5455C20.4795 19.5455 20.6597 19.4688 20.7925 19.3324C20.9254 19.1961 21 19.0111 21 18.8182C21 18.6253 20.9254 18.4403 20.7925 18.3039C20.6597 18.1675 20.4795 18.0909 20.2917 18.0909L15.3333 18.0909C15.3333 17.7051 15.1841 17.3352 14.9184 17.0624C14.6527 16.7896 14.2924 16.6364 13.9167 16.6364L12.5 16.6364C12.1243 16.6364 11.7639 16.7896 11.4983 17.0624C11.2326 17.3352 11.0833 17.7051 11.0833 18.0909L4.70833 18.0909C4.52047 18.0909 4.34031 18.1675 4.20747 18.3039C4.07463 18.4403 4 18.6253 4 18.8182C4 19.0111 4.07463 19.1961 4.20747 19.3324C4.34031 19.4688 4.52047 19.5455 4.70833 19.5455L11.0833 19.5455C11.0833 19.9312 11.2326 20.3012 11.4983 20.574C11.7639 20.8468 12.1243 21 12.5 21L13.9167 21C14.2924 21 14.6527 20.8468 14.9184 20.574C15.1841 20.3012 15.3333 19.9312 15.3333 19.5455ZM13.9167 18.0909L13.9167 19.5455L12.5 19.5455L12.5 18.0909L13.9167 18.0909Z"
        fill="#7b7b7b"
      />
    </Svg>
  </View>
);
export default SideStats;
