import * as React from 'react';
import Svg, { Circle, Path } from 'react-native-svg';
const UnHappy = (props) => (
  <Svg
    width={36}
    height={36}
    viewBox="0 0 36 36"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <Circle cx={17.9999} cy={17.9977} r={15.6774} fill="white" />
    <Path
      d="M10.188 18.378C10.3562 18.5448 10.5557 18.6768 10.775 18.7664C10.9943 18.856 11.2291 18.9014 11.466 18.9C11.7029 18.9014 11.9377 18.856 12.157 18.7664C12.3763 18.6768 12.5758 18.5448 12.744 18.378L15.444 15.678C15.6127 15.5107 15.7466 15.3116 15.838 15.0922C15.9294 14.8729 15.9764 14.6376 15.9764 14.4C15.9764 14.1624 15.9294 13.9271 15.838 13.7078C15.7466 13.4884 15.6127 13.2893 15.444 13.122L12.744 10.422C12.4051 10.0831 11.9453 9.89263 11.466 9.89263C10.9867 9.89263 10.527 10.0831 10.188 10.422C9.84906 10.7609 9.65864 11.2207 9.65864 11.7C9.65864 12.1793 9.84906 12.639 10.188 12.978L11.628 14.4L10.188 15.822C10.0193 15.9893 9.88539 16.1884 9.794 16.4078C9.70262 16.6271 9.65557 16.8624 9.65557 17.1C9.65557 17.3376 9.70262 17.5729 9.794 17.7922C9.88539 18.0116 10.0193 18.2107 10.188 18.378ZM23.688 18.378C23.8562 18.5448 24.0557 18.6768 24.275 18.7664C24.4943 18.856 24.7291 18.9014 24.966 18.9C25.2029 18.9014 25.4377 18.856 25.657 18.7664C25.8763 18.6768 26.0758 18.5448 26.244 18.378C26.4127 18.2107 26.5466 18.0116 26.638 17.7922C26.7294 17.5729 26.7764 17.3376 26.7764 17.1C26.7764 16.8624 26.7294 16.6271 26.638 16.4078C26.5466 16.1884 26.4127 15.9893 26.244 15.822L24.822 14.4L26.244 12.978C26.4118 12.8102 26.545 12.6109 26.6358 12.3916C26.7266 12.1724 26.7734 11.9373 26.7734 11.7C26.7734 11.4627 26.7266 11.2276 26.6358 11.0083C26.545 10.7891 26.4118 10.5898 26.244 10.422C26.0762 10.2542 25.8769 10.121 25.6577 10.0302C25.4384 9.93938 25.2034 9.89263 24.966 9.89263C24.7287 9.89263 24.4936 9.93938 24.2744 10.0302C24.0551 10.121 23.8558 10.2542 23.688 10.422L20.988 13.122C20.8193 13.2893 20.6854 13.4884 20.594 13.7078C20.5026 13.9271 20.4556 14.1624 20.4556 14.4C20.4556 14.6376 20.5026 14.8729 20.594 15.0922C20.6854 15.3116 20.8193 15.5107 20.988 15.678L23.688 18.378ZM23.886 21.978C23.5744 21.7443 23.1955 21.618 22.806 21.618C22.4165 21.618 22.0376 21.7443 21.726 21.978L20.394 22.968L19.08 21.978C18.7684 21.7443 18.3895 21.618 18 21.618C17.6105 21.618 17.2316 21.7443 16.92 21.978L15.606 22.968L14.274 21.978C13.9624 21.7443 13.5835 21.618 13.194 21.618C12.8045 21.618 12.4256 21.7443 12.114 21.978L9.72001 23.778C9.5309 23.9198 9.37159 24.0975 9.25115 24.3009C9.13072 24.5043 9.05153 24.7294 9.0181 24.9634C8.95058 25.436 9.07357 25.9161 9.36001 26.298C9.64644 26.6799 10.0729 26.9324 10.5454 26.9999C11.018 27.0674 11.4981 26.9444 11.88 26.658L13.194 25.668L14.526 26.658C14.5942 26.7011 14.6666 26.7373 14.742 26.766C14.8665 26.8325 14.9999 26.8809 15.138 26.91H15.372H15.786H16.002C16.2145 26.8473 16.4152 26.75 16.596 26.622L17.91 25.632L19.224 26.622C19.4798 26.8111 19.7811 26.929 20.0973 26.9638C20.4135 26.9986 20.7332 26.949 21.024 26.82L21.204 26.73C21.2754 26.7049 21.3423 26.6684 21.402 26.622L22.734 25.632L24.048 26.622C24.3596 26.8557 24.7385 26.982 25.128 26.982C25.4074 26.982 25.683 26.9169 25.933 26.792C26.1829 26.667 26.4003 26.4855 26.568 26.262C26.7098 26.0729 26.813 25.8577 26.8717 25.6287C26.9304 25.3997 26.9433 25.1614 26.9099 24.9274C26.8765 24.6934 26.7973 24.4683 26.6769 24.2649C26.5564 24.0615 26.3971 23.8838 26.208 23.742L23.886 21.978ZM18 0C14.4399 0 10.9598 1.05568 7.99974 3.03355C5.03966 5.01141 2.73255 7.82263 1.37018 11.1117C0.00779911 14.4008 -0.348661 18.02 0.345873 21.5116C1.04041 25.0033 2.75474 28.2106 5.27209 30.7279C7.78943 33.2453 10.9967 34.9596 14.4884 35.6541C17.98 36.3487 21.5992 35.9922 24.8883 34.6298C28.1774 33.2674 30.9886 30.9603 32.9665 28.0003C34.9443 25.0402 36 21.5601 36 18C36 15.6362 35.5344 13.2956 34.6298 11.1117C33.7253 8.92783 32.3994 6.94353 30.7279 5.27208C29.0565 3.60062 27.0722 2.27475 24.8883 1.37017C22.7044 0.465584 20.3638 0 18 0ZM18 32.4C15.152 32.4 12.3679 31.5554 9.99979 29.9732C7.63173 28.3909 5.78604 26.1419 4.69614 23.5106C3.60624 20.8794 3.32107 17.984 3.8767 15.1907C4.43233 12.3974 5.80379 9.83154 7.81767 7.81766C9.83154 5.80378 12.3974 4.43232 15.1907 3.87669C17.984 3.32106 20.8794 3.60623 23.5106 4.69613C26.1419 5.78604 28.3909 7.63172 29.9732 9.99979C31.5555 12.3679 32.4 15.1519 32.4 18C32.4 21.8191 30.8829 25.4818 28.1823 28.1823C25.4818 30.8829 21.8191 32.4 18 32.4Z"
      fill="#1B2936"
    />
  </Svg>
);
export default UnHappy;
