import * as React from 'react';
import { Svg, Path } from 'react-native-svg';

const ProgressIcon = ({ width, height, stroke }) => (
  <Svg
    width={width}
    height={height}
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <Path
      d="M0.787109 22.168H23.2129C23.3157 22.168 23.3961 22.2038 23.4443 22.2451C23.4912 22.2854 23.5 22.3216 23.5 22.3428C23.4999 22.364 23.4913 22.4002 23.4443 22.4404C23.3961 22.4818 23.3157 22.5176 23.2129 22.5176H0.787109C0.684275 22.5176 0.60385 22.4818 0.555664 22.4404C0.508765 22.4002 0.500076 22.364 0.5 22.3428C0.5 22.3216 0.508799 22.2854 0.555664 22.2451C0.603849 22.2038 0.684273 22.168 0.787109 22.168ZM3.45312 16.6396C3.55583 16.6397 3.63543 16.6755 3.68359 16.7168C3.73073 16.7572 3.74023 16.7932 3.74023 16.8145V19.3535C3.74012 19.3748 3.73043 19.411 3.68359 19.4512C3.63539 19.4923 3.55557 19.5283 3.45312 19.5283C3.35034 19.5283 3.26987 19.4925 3.22168 19.4512C3.17485 19.411 3.16613 19.3748 3.16602 19.3535V16.8145C3.16602 16.7933 3.17477 16.7572 3.22168 16.7168C3.2699 16.6754 3.35036 16.6396 3.45312 16.6396ZM9.15137 12.9609C9.2542 12.9609 9.33463 12.9967 9.38281 13.0381C9.42971 13.0783 9.4384 13.1145 9.43848 13.1357V19.3535C9.43836 19.3748 9.42964 19.411 9.38281 19.4512C9.33462 19.4925 9.25417 19.5283 9.15137 19.5283C9.04891 19.5283 8.96911 19.4923 8.9209 19.4512C8.87407 19.411 8.86437 19.3748 8.86426 19.3535V13.1357C8.86434 13.1145 8.87398 13.0783 8.9209 13.0381C8.9691 12.9969 9.04887 12.961 9.15137 12.9609ZM14.8496 8.92578C14.9521 8.92586 15.0319 8.96176 15.0801 9.00293C15.1269 9.04311 15.1366 9.07931 15.1367 9.10059V19.3525C15.1367 19.3738 15.1272 19.4107 15.0801 19.4512C15.0319 19.4923 14.952 19.5282 14.8496 19.5283C14.7469 19.5283 14.6664 19.4924 14.6182 19.4512C14.571 19.4107 14.5625 19.3738 14.5625 19.3525V9.10059C14.5626 9.07939 14.5713 9.04313 14.6182 9.00293C14.6543 8.97202 14.7084 8.94466 14.7764 8.93262L14.8496 8.92578ZM20.5469 5.02246C20.6497 5.02246 20.7301 5.05925 20.7783 5.10059C20.8252 5.1409 20.834 5.17707 20.834 5.19824V19.3535C20.8339 19.3748 20.8251 19.411 20.7783 19.4512C20.7301 19.4925 20.6497 19.5283 20.5469 19.5283C20.4445 19.5283 20.3646 19.4924 20.3164 19.4512C20.2693 19.4107 20.2598 19.3738 20.2598 19.3525V5.19824C20.2598 5.17707 20.2692 5.14104 20.3164 5.10059C20.3646 5.05925 20.4442 5.02251 20.5469 5.02246ZM18.0439 1.47656C18.1258 1.47267 18.2186 1.51207 18.2773 1.57617C18.3041 1.60539 18.3146 1.62877 18.3184 1.64648L18.3154 1.68457L17.1416 4.83887C17.1198 4.89651 17.0036 4.99756 16.8311 4.98242L16.7539 4.96875L16.6914 4.94629C16.6373 4.92026 16.6057 4.88567 16.5898 4.85938C16.571 4.82803 16.5699 4.80297 16.5791 4.77734L16.9639 3.74316L17.5137 2.2666L16.2129 3.15625L3.0293 12.1758C2.90006 12.2637 2.71795 12.2478 2.6123 12.1758L2.57227 12.1416L2.57129 12.1406C2.53414 12.1006 2.53168 12.069 2.53418 12.0498C2.53624 12.0341 2.5446 12.0067 2.57422 11.9766L2.61133 11.9463L16.0137 2.77734L17.3867 1.83789L15.7227 1.86426L14.1963 1.88867C14.0626 1.88425 13.9782 1.84389 13.9355 1.80859C13.9161 1.79243 13.9031 1.77704 13.8955 1.76172L13.8857 1.7168L13.8848 1.71582C13.8844 1.69526 13.8925 1.66023 13.9375 1.62012C13.9725 1.58895 14.0258 1.55996 14.0938 1.54688L14.166 1.54004L18.0283 1.47754H18.0361L18.0439 1.47656Z"
      stroke={stroke}
      strokeLinecap="round"
      strokeLinejoin="round"
      fill={stroke}
    />
  </Svg>
);

export default ProgressIcon;
