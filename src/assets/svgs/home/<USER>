import * as React from 'react';
import Svg, { Rect, Defs, Pattern, Use, Image } from 'react-native-svg';

const DoseCheckin = (props) => (
  <Svg
    width={118}
    height={118}
    viewBox="0 0 118 118"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    {...props}
  >
    <Rect width={118} height={118} fill="url(#pattern0_55426_6705)" />
    <Defs>
      <Pattern
        id="pattern0_55426_6705"
        patternContentUnits="objectBoundingBox"
        width={1}
        height={1}
      >
        <Use xlinkHref="#image0_55426_6705" transform="scale(0.00195312)" />
      </Pattern>
      <Image
        id="image0_55426_6705"
        width={512}
        height={512}
        preserveAspectRatio="none"
        xlinkHref="data:image/png;base64,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"
      />
    </Defs>
  </Svg>
);
export default DoseCheckin;
