import * as React from 'react';
import Svg, { Path } from 'react-native-svg';
const WhiteAlert = (props) => (
  <Svg
    width={127}
    height={113}
    viewBox="0 0 127 113"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <Path
      d="M121.447 113H5.55323C3.62167 113 1.82808 111.898 0.724335 110.244C-0.241445 108.59 -0.241445 106.523 0.724335 104.732L58.6711 2.7561C59.6369 0.964634 61.4305 0 63.5 0C65.5695 0 67.3631 1.10244 68.3289 2.7561L126.276 104.732C127.241 106.385 127.241 108.59 126.276 110.244C125.172 111.898 123.378 113 121.447 113ZM15.0731 101.976H112.065L63.5 16.6744L15.0731 101.976ZM69.0187 69.0402V43.4085C69.0187 40.3768 66.5353 37.8963 63.5 37.8963C60.4647 37.8963 57.9813 40.3768 57.9813 43.4085V69.0402C57.9813 72.072 60.4647 74.5524 63.5 74.5524C66.5353 74.5524 69.0187 72.072 69.0187 69.0402ZM68.3289 91.9158C69.5706 90.6756 70.3984 88.8841 70.3984 87.0927C70.3984 85.3012 69.7086 83.5098 68.3289 82.2695C67.0872 81.0293 65.2936 80.2024 63.5 80.2024C61.7064 80.2024 59.9128 80.8915 58.6711 82.2695C57.4294 83.5098 56.6016 85.3012 56.6016 87.0927C56.6016 88.8841 57.2914 90.6756 58.6711 91.9158C59.9128 93.1561 61.7064 93.9829 63.5 93.9829C65.2936 93.9829 67.0872 93.1561 68.3289 91.9158Z"
      fill="white"
    />
  </Svg>
);
export default WhiteAlert;
