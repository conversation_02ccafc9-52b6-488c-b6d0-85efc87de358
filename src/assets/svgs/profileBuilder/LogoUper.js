import { forwardRef } from 'react';
import Svg, { Path, Defs, LinearGradient, Stop } from 'react-native-svg';
const LogoUper = forwardRef((props, ref) => (
  <Svg
    width={79}
    height={42}
    viewBox="0 0 79 42"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    ref={ref}
    {...props}
  >
    <Path
      d="M49.5859 11.3355C50.3357 11.9955 51.0835 12.5939 51.7646 13.2591C52.7649 14.2361 53.7247 13.9261 54.4057 12.9187C55.3233 11.561 56.3185 10.2466 57.3725 8.98909C59.0558 6.98077 61.1205 5.39879 63.3512 4.0201C65.7843 2.5164 68.3862 1.41569 71.1743 0.748946C73.192 0.266433 75.229 -0.0501977 77.3158 0.00656612C78.4208 0.0366262 79.1493 1.03243 78.655 2.00173C77.5432 4.18214 76.3657 6.32969 75.2188 8.49264C74.6311 9.60105 74.058 10.717 73.4675 11.824C71.5568 15.4064 69.6429 18.9872 67.7266 22.5668C66.7028 24.4794 65.6678 26.3861 64.6448 28.2991C63.1056 31.1772 61.5737 34.0591 60.0365 36.9382C59.4182 38.0964 58.7799 39.2443 58.1783 40.4108C57.8392 41.0683 57.3561 41.388 56.5913 41.3858C51.6523 41.3711 46.7132 41.3662 41.7743 41.3922C41.0876 41.3958 40.5789 41.1651 40.2559 40.6246C39.9063 40.0395 40.1025 39.4627 40.4095 38.8778C41.1562 37.4549 42.022 36.0611 42.5278 34.553C43.6826 31.11 42.8431 28.0251 40.3439 25.3943C38.3066 23.2497 35.7345 22.3458 32.8244 22.6244C30.0782 22.8873 27.7363 24.0963 26.1993 26.4298C25.1512 28.021 24.3115 29.748 23.3988 31.4251C21.7148 34.5194 20.0637 37.6315 18.3438 40.7061C18.1609 41.033 17.6128 41.3467 17.2314 41.3498C12.1274 41.3922 7.02292 41.3826 1.91856 41.3747C0.821713 41.373 0.108819 40.2803 0.603047 39.2757C1.3757 37.705 2.22488 36.1709 3.05177 34.6267C3.68135 33.4509 4.33359 32.2871 4.96364 31.1115C6.78744 27.7088 8.57679 24.2875 10.4378 20.9049C11.2995 19.3386 12.2332 17.8025 13.2476 16.3293C14.5305 14.466 16.1806 12.9309 17.9464 11.5036C19.4874 10.258 21.1736 9.26863 22.9544 8.42403C25.0604 7.42513 27.2729 6.77669 29.5791 6.37356C31.5512 6.02882 33.5312 5.93183 35.5072 6.04864C38.3702 6.21788 41.1393 6.88574 43.7931 7.9893C45.8588 8.84832 47.7832 9.94428 49.5859 11.3355Z"
      fill="url(#paint0_linear_651_8390)"
    />
    <Defs>
      <LinearGradient
        id="paint0_linear_651_8390"
        x1={56.9543}
        y1={59.8808}
        x2={42.329}
        y2={2.34556}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#04E0BC" />
        <Stop offset={0.684443} stopColor="#05D4DD" />
      </LinearGradient>
    </Defs>
  </Svg>
));
export default LogoUper;
