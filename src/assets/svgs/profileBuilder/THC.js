import * as React from 'react';
import Svg, { Path, Mask } from 'react-native-svg';
import { ThemeContext } from '../../../context/ThemeContext';
const THC = (props) => {
  const { theme } = React.useContext(ThemeContext);
  return (
    <Svg
      width={79}
      height={82}
      viewBox="0 0 79 82"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        d="M35.5 72.5L39.5 69L42 71.5L46 74V72L43.5 69L48 70L55 69L58.5 67.5L56.5 66L49 65L46 64.5L48 63L52 59L55.5 53.5V51L46.5 55L42.5 59.5L43.5 56V48.5L41.5 42.5L39.5 37H38.5L35.5 45L34.5 53.5L36 59L34.5 58.5L28.5 53.5L22.5 51.5L23.5 54.5L28 61L32 65L25.5 65.5L20 67.5L27 69.5L32.5 70L32 74.5L35.5 72.5Z"
        fill="#05ECF6"
      />
      <Mask id="path-2-inside-1_776_8650" fill="white">
        <Path d="M6.29708 81.3753H71.7982C75.2708 81.3753 78.0952 78.4643 78.0952 74.8852C78.0952 73.6001 77.7295 72.3538 77.0384 71.2847L49.4603 28.6512V10.277H51.4127C53.9248 10.277 55.9683 8.17093 55.9683 5.58188C55.9683 2.99284 53.9248 0.886719 51.4127 0.886719H26.6825C24.1705 0.886719 22.127 2.99284 22.127 5.58188C22.127 8.17093 24.1705 10.277 26.6825 10.277H28.6349V28.6512L1.05689 71.286C0.365746 72.3538 0 73.6001 0 74.8852C0 78.4643 2.82444 81.3753 6.29708 81.3753ZM36.0396 54.1285C36.0396 48.1107 37.8189 42.6348 39.0476 39.5695C40.2763 42.6348 42.0556 48.1107 42.0556 54.1285C42.0556 56.621 40.4664 60.9486 39.1517 64.0434C39.1166 64.1145 39.0815 64.1829 39.0476 64.2527C39.0138 64.1829 38.9786 64.1132 38.9435 64.0421C37.6289 60.9486 36.0396 56.625 36.0396 54.1285ZM43.2934 70.7454C43.8596 71.1841 44.3308 71.6563 44.7199 72.1244C44.0509 71.7596 43.3533 71.3236 42.7012 70.8192C42.1259 70.3752 41.695 69.9405 41.3671 69.5381C41.3801 69.5421 41.3931 69.5448 41.4061 69.5488C41.9697 69.8305 42.6049 70.2128 43.2934 70.7454ZM43.9012 67.4172C45.8224 66.941 48.0364 66.5144 49.8183 66.5144C51.5988 66.5144 53.6736 66.9303 55.4385 67.3931C53.6736 67.8572 51.5975 68.2717 49.8183 68.2717C48.0364 68.2731 45.8237 67.8706 43.9012 67.4172ZM28.2757 68.2731C26.4951 68.2731 24.4204 67.8572 22.6554 67.3944C24.4204 66.9303 26.4964 66.5157 28.2757 66.5157C30.0589 66.5157 32.2716 66.941 34.1927 67.4185C32.2716 67.8706 30.0589 68.2731 28.2757 68.2731ZM34.8018 70.7454C35.4904 70.2128 36.1269 69.8305 36.6891 69.5488C36.7022 69.5448 36.7152 69.5421 36.7282 69.5381C36.4002 69.9405 35.9681 70.3752 35.3941 70.8192C34.7446 71.3209 34.0469 71.7555 33.3779 72.1218C33.7658 71.6536 34.2357 71.1827 34.8018 70.7454ZM3.22403 72.7737L31.0194 29.8022C31.1613 29.5822 31.2381 29.322 31.2381 29.0577V8.93557C31.2381 8.19508 30.655 7.5941 29.9365 7.5941H26.6825C25.6061 7.5941 24.7302 6.69128 24.7302 5.58188C24.7302 4.47248 25.6061 3.56967 26.6825 3.56967H51.4127C52.4891 3.56967 53.3651 4.47248 53.3651 5.58188C53.3651 6.69128 52.4891 7.5941 51.4127 7.5941H48.1587C47.4403 7.5941 46.8571 8.19508 46.8571 8.93557V29.0577C46.8571 29.322 46.9339 29.5822 47.0758 29.8022L74.8712 72.7737C75.2773 73.4015 75.4921 74.1313 75.4921 74.8852C75.4921 76.9846 73.8351 78.6923 71.7982 78.6923H40.3492V72.2921C40.5913 72.5162 40.849 72.7415 41.1393 72.9656C43.8635 75.0717 47.1552 76.0952 47.2945 76.1381C47.4168 76.1757 47.5418 76.1945 47.6654 76.1945C47.996 76.1945 48.3188 76.0644 48.5648 75.8229C48.9019 75.4889 49.0412 74.9952 48.928 74.5284C48.9032 74.4264 48.4425 72.6463 46.9691 70.732C47.9544 70.8702 48.9267 70.9574 49.8196 70.9574C54.3126 70.9574 60.0865 68.7453 60.3299 68.6514C60.8375 68.4555 61.1746 67.9538 61.1746 67.3944C61.1746 66.835 60.8375 66.3346 60.3299 66.1374C60.0865 66.0435 54.3126 63.8314 49.8196 63.8314C49.5749 63.8314 49.3224 63.8408 49.0672 63.8542C49.8898 63.2184 50.6994 62.5208 51.4583 61.7387C55.2055 57.8766 57.4091 51.8923 57.5015 51.6401C57.6733 51.1666 57.5731 50.634 57.2412 50.2611C56.9093 49.8895 56.4004 49.7392 55.9305 49.8774C55.6715 49.9525 49.5397 51.7595 45.3968 55.3184C45.0714 55.5975 44.7577 55.8993 44.4505 56.2105C44.5807 55.466 44.6601 54.7604 44.6601 54.1285C44.6601 44.2781 40.3752 36.0683 40.1917 35.7235C39.7349 34.8609 38.363 34.8609 37.9061 35.7235C37.7239 36.0683 33.4378 44.2781 33.4378 54.1285C33.4378 54.7604 33.5172 55.466 33.6473 56.2105C33.3389 55.898 33.0252 55.5975 32.7011 55.3184C28.5568 51.7595 22.4263 49.9525 22.1673 49.8774C21.6949 49.7379 21.1885 49.8881 20.8566 50.2611C20.5247 50.6327 20.4232 51.1666 20.5963 51.6401C20.6887 51.8923 22.8923 57.8766 26.6396 61.7387C27.3984 62.5208 28.208 63.2184 29.0306 63.8542C28.7755 63.8408 28.523 63.8314 28.2783 63.8314C23.7852 63.8314 18.0114 66.0435 17.768 66.1374C17.2577 66.3346 16.9206 66.835 16.9206 67.3944C16.9206 67.9538 17.2577 68.4542 17.7654 68.6514C18.0088 68.7453 23.7826 70.9574 28.2757 70.9574C29.1686 70.9574 30.1409 70.8702 31.1262 70.732C29.6528 72.6463 29.192 74.4264 29.1673 74.5284C29.054 74.9965 29.1933 75.4902 29.5304 75.8229C29.7764 76.0657 30.1005 76.1945 30.4298 76.1945C30.5535 76.1945 30.6784 76.1757 30.8008 76.1381C30.94 76.0952 34.2317 75.0717 36.956 72.9656C37.2462 72.7415 37.5026 72.5162 37.746 72.2921V78.6923H6.29708C4.2601 78.6923 2.60317 76.9846 2.60317 74.8852C2.60317 74.1313 2.81794 73.4015 3.22403 72.7737ZM41.5284 65.1568C42.7207 62.7797 44.6744 59.4327 47.0615 57.3803C49.1284 55.6055 51.8435 54.2868 53.8545 53.4645C52.9134 55.3909 51.4583 57.945 49.6165 59.8419C47.1474 62.388 43.9273 64.136 41.5284 65.1568ZM36.5668 65.1568C34.168 64.1373 30.9478 62.388 28.4774 59.8432C26.6409 57.9504 25.1857 55.3949 24.2421 53.4659C26.253 54.2895 28.9668 55.6069 31.0324 57.3803C33.4222 59.4327 35.3758 62.7784 36.5668 65.1568Z" />
      </Mask>
      <Path
        d="M6.29708 81.3753H71.7982C75.2708 81.3753 78.0952 78.4643 78.0952 74.8852C78.0952 73.6001 77.7295 72.3538 77.0384 71.2847L49.4603 28.6512V10.277H51.4127C53.9248 10.277 55.9683 8.17093 55.9683 5.58188C55.9683 2.99284 53.9248 0.886719 51.4127 0.886719H26.6825C24.1705 0.886719 22.127 2.99284 22.127 5.58188C22.127 8.17093 24.1705 10.277 26.6825 10.277H28.6349V28.6512L1.05689 71.286C0.365746 72.3538 0 73.6001 0 74.8852C0 78.4643 2.82444 81.3753 6.29708 81.3753ZM36.0396 54.1285C36.0396 48.1107 37.8189 42.6348 39.0476 39.5695C40.2763 42.6348 42.0556 48.1107 42.0556 54.1285C42.0556 56.621 40.4664 60.9486 39.1517 64.0434C39.1166 64.1145 39.0815 64.1829 39.0476 64.2527C39.0138 64.1829 38.9786 64.1132 38.9435 64.0421C37.6289 60.9486 36.0396 56.625 36.0396 54.1285ZM43.2934 70.7454C43.8596 71.1841 44.3308 71.6563 44.7199 72.1244C44.0509 71.7596 43.3533 71.3236 42.7012 70.8192C42.1259 70.3752 41.695 69.9405 41.3671 69.5381C41.3801 69.5421 41.3931 69.5448 41.4061 69.5488C41.9697 69.8305 42.6049 70.2128 43.2934 70.7454ZM43.9012 67.4172C45.8224 66.941 48.0364 66.5144 49.8183 66.5144C51.5988 66.5144 53.6736 66.9303 55.4385 67.3931C53.6736 67.8572 51.5975 68.2717 49.8183 68.2717C48.0364 68.2731 45.8237 67.8706 43.9012 67.4172ZM28.2757 68.2731C26.4951 68.2731 24.4204 67.8572 22.6554 67.3944C24.4204 66.9303 26.4964 66.5157 28.2757 66.5157C30.0589 66.5157 32.2716 66.941 34.1927 67.4185C32.2716 67.8706 30.0589 68.2731 28.2757 68.2731ZM34.8018 70.7454C35.4904 70.2128 36.1269 69.8305 36.6891 69.5488C36.7022 69.5448 36.7152 69.5421 36.7282 69.5381C36.4002 69.9405 35.9681 70.3752 35.3941 70.8192C34.7446 71.3209 34.0469 71.7555 33.3779 72.1218C33.7658 71.6536 34.2357 71.1827 34.8018 70.7454ZM3.22403 72.7737L31.0194 29.8022C31.1613 29.5822 31.2381 29.322 31.2381 29.0577V8.93557C31.2381 8.19508 30.655 7.5941 29.9365 7.5941H26.6825C25.6061 7.5941 24.7302 6.69128 24.7302 5.58188C24.7302 4.47248 25.6061 3.56967 26.6825 3.56967H51.4127C52.4891 3.56967 53.3651 4.47248 53.3651 5.58188C53.3651 6.69128 52.4891 7.5941 51.4127 7.5941H48.1587C47.4403 7.5941 46.8571 8.19508 46.8571 8.93557V29.0577C46.8571 29.322 46.9339 29.5822 47.0758 29.8022L74.8712 72.7737C75.2773 73.4015 75.4921 74.1313 75.4921 74.8852C75.4921 76.9846 73.8351 78.6923 71.7982 78.6923H40.3492V72.2921C40.5913 72.5162 40.849 72.7415 41.1393 72.9656C43.8635 75.0717 47.1552 76.0952 47.2945 76.1381C47.4168 76.1757 47.5418 76.1945 47.6654 76.1945C47.996 76.1945 48.3188 76.0644 48.5648 75.8229C48.9019 75.4889 49.0412 74.9952 48.928 74.5284C48.9032 74.4264 48.4425 72.6463 46.9691 70.732C47.9544 70.8702 48.9267 70.9574 49.8196 70.9574C54.3126 70.9574 60.0865 68.7453 60.3299 68.6514C60.8375 68.4555 61.1746 67.9538 61.1746 67.3944C61.1746 66.835 60.8375 66.3346 60.3299 66.1374C60.0865 66.0435 54.3126 63.8314 49.8196 63.8314C49.5749 63.8314 49.3224 63.8408 49.0672 63.8542C49.8898 63.2184 50.6994 62.5208 51.4583 61.7387C55.2055 57.8766 57.4091 51.8923 57.5015 51.6401C57.6733 51.1666 57.5731 50.634 57.2412 50.2611C56.9093 49.8895 56.4004 49.7392 55.9305 49.8774C55.6715 49.9525 49.5397 51.7595 45.3968 55.3184C45.0714 55.5975 44.7577 55.8993 44.4505 56.2105C44.5807 55.466 44.6601 54.7604 44.6601 54.1285C44.6601 44.2781 40.3752 36.0683 40.1917 35.7235C39.7349 34.8609 38.363 34.8609 37.9061 35.7235C37.7239 36.0683 33.4378 44.2781 33.4378 54.1285C33.4378 54.7604 33.5172 55.466 33.6473 56.2105C33.3389 55.898 33.0252 55.5975 32.7011 55.3184C28.5568 51.7595 22.4263 49.9525 22.1673 49.8774C21.6949 49.7379 21.1885 49.8881 20.8566 50.2611C20.5247 50.6327 20.4232 51.1666 20.5963 51.6401C20.6887 51.8923 22.8923 57.8766 26.6396 61.7387C27.3984 62.5208 28.208 63.2184 29.0306 63.8542C28.7755 63.8408 28.523 63.8314 28.2783 63.8314C23.7852 63.8314 18.0114 66.0435 17.768 66.1374C17.2577 66.3346 16.9206 66.835 16.9206 67.3944C16.9206 67.9538 17.2577 68.4542 17.7654 68.6514C18.0088 68.7453 23.7826 70.9574 28.2757 70.9574C29.1686 70.9574 30.1409 70.8702 31.1262 70.732C29.6528 72.6463 29.192 74.4264 29.1673 74.5284C29.054 74.9965 29.1933 75.4902 29.5304 75.8229C29.7764 76.0657 30.1005 76.1945 30.4298 76.1945C30.5535 76.1945 30.6784 76.1757 30.8008 76.1381C30.94 76.0952 34.2317 75.0717 36.956 72.9656C37.2462 72.7415 37.5026 72.5162 37.746 72.2921V78.6923H6.29708C4.2601 78.6923 2.60317 76.9846 2.60317 74.8852C2.60317 74.1313 2.81794 73.4015 3.22403 72.7737ZM41.5284 65.1568C42.7207 62.7797 44.6744 59.4327 47.0615 57.3803C49.1284 55.6055 51.8435 54.2868 53.8545 53.4645C52.9134 55.3909 51.4583 57.945 49.6165 59.8419C47.1474 62.388 43.9273 64.136 41.5284 65.1568ZM36.5668 65.1568C34.168 64.1373 30.9478 62.388 28.4774 59.8432C26.6409 57.9504 25.1857 55.3949 24.2421 53.4659C26.253 54.2895 28.9668 55.6069 31.0324 57.3803C33.4222 59.4327 35.3758 62.7784 36.5668 65.1568Z"
        fill={props.isSelected ? props.fill : theme.colors.text}
        stroke="#1B2936"
        strokeWidth={2}
        mask="url(#path-2-inside-1_776_8650)"
      />
      <Path
        d="M43.7539 17.4844V27.2158H42.1504V17.4844H43.7539Z"
        fill={props.isSelected ? props.fill : theme.colors.text}
        stroke="#1B2936"
      />
      <Path
        d="M43.7539 12.1172V13.7998H42.1504V12.1172H43.7539Z"
        fill={props.isSelected ? props.fill : theme.colors.text}
        stroke="#1B2936"
      />
    </Svg>
  );
};
export default THC;
