import { forwardRef } from 'react';
import Svg, { Path, Defs, LinearGradient, Stop } from 'react-native-svg';
const LogoLower = forwardRef((props, ref) => (
  <Svg
    width={80}
    height={43}
    viewBox="0 0 80 43"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    ref={ref}
    {...props}
  >
    <Path
      d="M30.1772 30.6475C29.422 29.9831 28.669 29.3807 27.9831 28.7111C26.9757 27.7276 26.0091 28.0396 25.3234 29.0538C24.3993 30.4205 23.3971 31.7437 22.3357 33.0096C20.6406 35.0313 18.5613 36.6238 16.3149 38.0117C13.8647 39.5254 11.2444 40.6335 8.43672 41.3047C6.40483 41.7904 4.35341 42.1091 2.25199 42.052C1.13917 42.0217 0.660766 41.1027 1.15851 40.127C2.27814 37.932 3.45292 35.6816 4.60786 33.5042C5.19972 32.3884 6.08289 31.0312 6.67747 29.9169C8.60167 26.3106 10.4047 23.0342 12.3344 19.4308C13.3655 17.5055 14.4776 15.2857 15.5078 13.36C17.0578 10.4627 18.513 7.84185 20.061 4.94352C20.6837 3.77767 20.9728 3.22037 21.5787 2.04607C21.9201 1.38426 22.3522 0.394642 23.1224 0.396919C28.0962 0.411641 33.0701 0.416642 38.0438 0.390414C38.7353 0.386771 39.2476 0.619079 39.5728 1.16318C39.9249 1.75218 39.7273 2.33285 39.4182 2.92158C38.6662 4.35396 37.7943 5.75708 37.2849 7.27521C36.122 10.7412 36.9674 13.8466 39.4842 16.495C41.5359 18.6539 44.126 19.5638 47.0567 19.2834C49.8222 19.0188 52.1806 17.8017 53.7285 15.4526C54.7839 13.8508 55.6295 12.1123 56.5486 10.424C58.2445 7.30906 59.9072 4.17617 61.6393 1.08115C61.8235 0.752005 62.3754 0.436266 62.7595 0.433079C67.8994 0.390406 73.0399 0.400056 78.1802 0.408057C79.2847 0.409777 79.8877 1.31063 79.3899 2.32201C78.6119 3.90317 77.8771 5.18265 77.0444 6.73719C76.4104 7.9208 75.3333 9.83103 74.6988 11.0144C72.7672 14.7397 71.4679 17.2674 69.5938 20.6726C68.726 22.2493 67.7929 24.1374 66.7714 25.6205C65.4794 27.4962 63.8177 29.0415 62.0395 30.4783C60.4876 31.7322 58.7895 32.7282 56.9962 33.5784C54.8753 34.584 52.6473 35.2367 50.3248 35.6425C48.3388 35.9896 46.3449 36.0872 44.3549 35.9696C41.4718 35.7993 38.6832 35.127 36.0108 34.016C33.9305 33.1513 31.9925 32.048 30.1772 30.6475Z"
      fill="url(#paint0_linear_651_8391)"
    />
    <Defs>
      <LinearGradient
        id="paint0_linear_651_8391"
        x1={42.6303}
        y1={-7.61213}
        x2={52.8404}
        y2={42.0586}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#05D4DD" />
        <Stop offset={0.706514} stopColor="#04E0BC" />
      </LinearGradient>
    </Defs>
  </Svg>
));
export default LogoLower;
