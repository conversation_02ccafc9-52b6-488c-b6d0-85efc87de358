import * as React from 'react';
import Svg, { G, Rect, <PERSON>, Defs, ClipPath } from 'react-native-svg';
import { ThemeContext } from '../../../context/ThemeContext';
const Oral = (props) => {
  const { theme } = React.useContext(ThemeContext);
  return (
    <Svg
      width={100}
      height={100}
      viewBox="0 0 125 125"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <G clipPath="url(#clip0_756_8646)">
        <Rect
          x={44.9219}
          y={55.0117}
          width={35.1562}
          height={36.675}
          fill="#05ECF6"
        />
        <Path
          d="M39.0625 8.14844V24.4484H42.9688V28.603L31.25 41.3573V110.023H29.2969C26.0677 110.023 23.4375 112.767 23.4375 116.136C23.4375 119.505 26.0677 122.248 29.2969 122.248H44.9219C47.4631 122.248 49.6133 120.539 50.4227 118.173H53.5355L55.7289 120.462C58.0123 122.844 61.731 122.844 64.0144 120.462C64.6527 119.796 65.0948 119.008 65.3763 118.173H74.5773C75.3867 120.539 77.5369 122.248 80.0781 122.248H95.7031C98.9323 122.248 101.562 119.505 101.562 116.136C101.562 112.767 98.9323 110.023 95.7031 110.023H93.75V41.3573L82.0312 28.603V24.4484H85.9375V8.14844H39.0625ZM41.0156 10.1859H83.9844V22.4109H41.0156V10.1859ZM44.9219 24.4484H80.0781V28.5234H44.9219V24.4484ZM43.8728 30.5609H81.1272L91.7969 42.1771V52.9734H33.2031V42.1771L43.8728 30.5609ZM33.2031 55.0109H44.9219V91.6859H33.2031V55.0109ZM46.875 55.0109H78.125V91.6859H46.875V55.0109ZM80.0781 55.0109H91.7969V91.6859H80.0781V55.0109ZM33.2031 93.7234H91.7969V110.023H80.0781C76.8489 110.023 74.2188 112.767 74.2188 116.136C74.2188 116.505 74.2612 116.863 74.3217 117.214C74.2557 116.865 74.2188 116.506 74.2188 116.136H65.7272C65.7263 114.572 65.1552 113.008 64.0144 111.818L59.8717 107.496L58.4908 106.056L52.9671 100.294C50.6837 97.9116 46.9649 97.9116 44.6815 100.294C42.3982 102.676 42.3982 106.555 44.6815 108.937L45.8107 110.115C45.5187 110.068 45.2262 110.023 44.9219 110.023H33.2031V93.7234ZM48.8243 100.524C49.8198 100.524 50.8151 100.93 51.5862 101.734L57.1098 107.496L51.5862 113.259L46.0625 107.496C44.5203 105.888 44.5203 103.343 46.0625 101.734C46.8336 100.93 47.8288 100.524 48.8243 100.524ZM58.4908 108.937L62.6335 113.259C64.1757 114.868 64.1757 117.412 62.6335 119.021C61.0913 120.63 58.652 120.63 57.1098 119.021L52.9671 114.699L58.4908 108.937ZM29.2969 112.061H35.1562V120.211H29.2969C27.1159 120.211 25.3906 118.411 25.3906 116.136C25.3906 113.861 27.1159 112.061 29.2969 112.061ZM37.1094 112.061H44.9219C47.1028 112.061 48.8281 113.861 48.8281 116.136C48.8281 118.411 47.1028 120.211 44.9219 120.211H37.1094V112.061ZM80.0781 112.061H87.8906V120.211H80.0781C77.8972 120.211 76.1719 118.411 76.1719 116.136C76.1719 113.861 77.8972 112.061 80.0781 112.061ZM89.8438 112.061H95.7031C97.8841 112.061 99.6094 113.861 99.6094 116.136C99.6094 118.411 97.8841 120.211 95.7031 120.211H89.8438V112.061ZM50.6935 115.209L51.5823 116.136H50.7812C50.7812 116.506 50.7443 116.865 50.6783 117.214C50.7388 116.863 50.7812 116.505 50.7812 116.136C50.7812 115.818 50.7385 115.513 50.6935 115.209ZM65.7158 116.283C65.708 116.583 65.6846 116.883 65.6319 117.179C65.6804 116.882 65.7091 116.583 65.7158 116.283Z"
          fill={props.isSelected ? props.fill : theme.colors.text}
        />
        <Path
          d="M62.5076 59.0859L61.7599 60.0012C61.5048 60.3144 61.2646 60.6445 61.039 60.9881C60.8133 61.3318 60.6014 61.6882 60.4057 62.0586C60.0144 62.7994 59.6856 63.5912 59.4177 64.4185C58.882 66.0731 58.596 67.8732 58.5938 69.7191V69.7231C58.5938 69.8207 58.6035 69.9163 58.6052 70.0136C57.7699 69.3125 56.8941 68.6831 55.9616 68.2149C55.3135 67.8895 54.6463 67.622 53.9742 67.423C53.6381 67.3235 53.3019 67.2416 52.9633 67.1762C52.6246 67.1109 52.2836 67.0623 51.9447 67.033L50.7812 66.9335L50.8919 68.1433C50.9231 68.4885 50.9735 68.8376 51.0406 69.1819C51.1078 69.5263 51.1913 69.8676 51.2924 70.2086C51.4946 70.8907 51.7625 71.5631 52.0897 72.2183C52.744 73.5286 53.6364 74.7673 54.7218 75.8595V75.8635C54.8153 75.9572 54.9238 76.0274 55.0194 76.1182C54.8997 76.1648 54.7703 76.1846 54.6532 76.2375C54.1781 76.4521 53.7255 76.714 53.3028 77.0255C53.0914 77.1812 52.8876 77.3521 52.6924 77.5309C52.4972 77.7096 52.3123 77.899 52.1355 78.0999L51.5251 78.7884L52.1355 79.4768C52.3125 79.6775 52.4971 79.8674 52.6924 80.0459C52.8877 80.2244 53.0913 80.3919 53.3028 80.5473C53.7257 80.8582 54.1819 81.1213 54.657 81.3353C55.1321 81.5492 55.6294 81.7141 56.1409 81.8247C56.6524 81.9354 57.1767 81.9914 57.7087 81.9919H57.7126C58.2449 81.9911 58.7687 81.9358 59.2804 81.8247C59.7921 81.7137 60.2891 81.5497 60.7643 81.3353C61.0315 81.2147 61.2719 81.0417 61.5234 80.8896V87.6109H63.4766V80.8896C63.7281 81.0417 63.9685 81.2147 64.2357 81.3353C64.7109 81.5497 65.2079 81.7137 65.7196 81.8248C66.2313 81.9358 66.7551 81.9911 67.2874 81.9919H67.2913C67.8233 81.9914 68.3476 81.9354 68.8591 81.8248C69.3706 81.7141 69.8679 81.5493 70.343 81.3353C70.8181 81.1213 71.2743 80.8582 71.6972 80.5473C71.9087 80.3919 72.1123 80.2244 72.3076 80.0459C72.5029 79.8675 72.6875 79.6775 72.8645 79.4769L73.4749 78.7884L72.8645 78.1C72.6877 77.899 72.5028 77.7097 72.3076 77.5309C72.1124 77.3521 71.9086 77.1812 71.6972 77.0255C71.2745 76.714 70.8219 76.4521 70.3468 76.2376C70.2297 76.1847 70.1003 76.1649 69.9806 76.1182C70.0762 76.0274 70.1847 75.9572 70.2782 75.8635V75.8595C71.3636 74.7673 72.256 73.5286 72.9103 72.2183C73.2375 71.5631 73.5054 70.8907 73.7076 70.2086C73.8087 69.8676 73.8922 69.5263 73.9594 69.1819C74.0265 68.8376 74.0769 68.4885 74.1081 68.1433L74.2188 66.9335L73.0553 67.033C72.7164 67.0623 72.3754 67.1109 72.0367 67.1762C71.6981 67.2416 71.3619 67.3235 71.0258 67.423C70.3537 67.622 69.6865 67.8895 69.0384 68.2149C68.1059 68.6831 67.2301 69.3125 66.3948 70.0136C66.3965 69.9162 66.4061 69.8208 66.4062 69.7231V69.7191C66.405 67.8741 66.1242 66.073 65.5899 64.4185C65.3228 63.5912 64.9925 62.8036 64.6019 62.0626C64.4066 61.6921 64.1939 61.3319 63.9687 60.9881C63.7434 60.6443 63.5062 60.3146 63.2515 60.0012L62.5076 59.0859ZM62.5038 62.3889C62.6358 62.6027 62.7691 62.8139 62.8891 63.0416C63.2208 63.6709 63.5079 64.353 63.7398 65.0711C64.2032 66.5061 64.4516 68.0927 64.4531 69.7191C64.4522 70.5783 64.3738 71.4223 64.2395 72.2421C63.6869 72.96 63.186 73.7048 62.7937 74.4906C62.6858 74.7067 62.595 74.9276 62.5 75.1472C62.405 74.9276 62.3142 74.7067 62.2063 74.4906C61.814 73.7048 61.3131 72.96 60.7605 72.2421C60.626 71.4222 60.5475 70.578 60.5469 69.7191C60.5493 68.0919 60.8033 66.506 61.2679 65.0711C61.5003 64.353 61.7862 63.6707 62.1185 63.0416C62.2385 62.8144 62.3719 62.6022 62.5038 62.3889ZM53.0815 69.2973C53.2016 69.3276 53.3197 69.3492 53.4401 69.3849C53.9987 69.5502 54.5632 69.7725 55.1147 70.0494C56.2179 70.6033 57.2798 71.3716 58.2199 72.3138C59.1579 73.2576 59.9229 74.3252 60.4744 75.4297C60.6123 75.7059 60.7375 75.9864 60.8482 76.2654C60.8509 76.2721 60.8532 76.2786 60.8559 76.2853C60.8241 76.2705 60.7963 76.248 60.7643 76.2336C60.2891 76.0195 59.792 75.8548 59.2804 75.7441C58.7688 75.6334 58.2409 75.5775 57.7087 75.577C57.6338 75.5771 57.5622 75.5984 57.4875 75.6008C56.9971 75.237 56.5197 74.8359 56.076 74.3911C55.1393 73.448 54.3725 72.3825 53.8216 71.2791C53.5459 70.727 53.3232 70.1656 53.1578 69.6077C53.1271 69.504 53.1083 69.4007 53.0815 69.2973ZM71.9185 69.2973C71.8917 69.4007 71.8729 69.504 71.8422 69.6077C71.6768 70.1656 71.4541 70.727 71.1784 71.2791C70.6275 72.3825 69.8607 73.448 68.924 74.3911C68.4803 74.8359 68.0029 75.237 67.5125 75.6008C67.4378 75.5984 67.3662 75.5771 67.2913 75.5769C66.7591 75.5775 66.2312 75.6334 65.7196 75.7441C65.208 75.8548 64.7109 76.0195 64.2357 76.2336C64.2037 76.248 64.1759 76.2705 64.1441 76.2853C64.1468 76.2786 64.1491 76.2721 64.1518 76.2654C64.2625 75.9864 64.3877 75.7058 64.5256 75.4297C65.0771 74.3252 65.8421 73.2576 66.7801 72.3138C67.7202 71.3715 68.7821 70.6033 69.8853 70.0494C70.4368 69.7725 71.0013 69.5502 71.5599 69.3849C71.6803 69.3492 71.7984 69.3276 71.9185 69.2973ZM57.7087 77.6145C58.1075 77.6148 58.5014 77.6591 58.8837 77.7418C59.266 77.8245 59.6367 77.9448 59.9899 78.1039C60.3431 78.2631 60.6778 78.4598 60.9894 78.6889C61.0302 78.7189 61.0676 78.7533 61.1076 78.7844C61.0678 78.8155 61.03 78.85 60.9894 78.8799C60.6779 79.1094 60.3431 79.3055 59.9899 79.4649C59.6367 79.6243 59.266 79.748 58.8837 79.831C58.5013 79.914 58.1076 79.9538 57.7087 79.9544C57.3101 79.954 56.9198 79.9137 56.5376 79.831C56.1554 79.7483 55.7845 79.624 55.4314 79.4649C55.0782 79.3059 54.7397 79.113 54.4281 78.8839C54.3881 78.8545 54.3529 78.8189 54.3137 78.7884C54.353 78.7577 54.3879 78.7225 54.4281 78.6929C54.7396 78.4634 55.0782 78.2674 55.4314 78.1079C55.7845 77.9484 56.1553 77.8249 56.5376 77.7418C56.9187 77.659 57.3112 77.6154 57.7087 77.6145ZM67.2913 77.6145C67.6888 77.6154 68.0813 77.659 68.4624 77.7418C68.8447 77.8249 69.2155 77.9484 69.5686 78.1079C69.9218 78.2674 70.2604 78.4634 70.5719 78.6929C70.6121 78.7225 70.647 78.7577 70.6863 78.7884C70.6471 78.8189 70.6119 78.8545 70.5719 78.8839C70.2603 79.113 69.9218 79.3059 69.5686 79.4649C69.2155 79.624 68.8446 79.7483 68.4624 79.831C68.0802 79.9137 67.6899 79.954 67.2913 79.9544C66.8924 79.9538 66.4987 79.914 66.1163 79.831C65.734 79.748 65.3633 79.6243 65.0101 79.4649C64.6569 79.3055 64.3221 79.1094 64.0106 78.8799C63.97 78.85 63.9322 78.8155 63.8924 78.7844C63.9324 78.7533 63.9698 78.7189 64.0106 78.6889C64.3222 78.4598 64.6569 78.2631 65.0101 78.1039C65.3633 77.9448 65.734 77.8245 66.1163 77.7418C66.4986 77.6591 66.8925 77.6148 67.2913 77.6145Z"
          fill="black"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_756_8646">
          <Rect width={125} height={125} fill="white" />
        </ClipPath>
      </Defs>
    </Svg>
  );
};
export default Oral;
