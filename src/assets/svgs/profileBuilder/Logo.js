import * as React from 'react';
import Svg, { Path, Defs, LinearGradient, Stop } from 'react-native-svg';
const Logo = (props) => (
  <Svg
    width={72}
    height={64}
    viewBox="0 0 72 64"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <Path
      d="M52.3056 7.66477C52.8109 8.11102 53.3148 8.51566 53.7737 8.96547C54.4478 9.62604 55.0945 9.41647 55.5534 8.73529C56.1717 7.81725 56.8423 6.92844 57.5525 6.07818C58.6868 4.72021 60.0781 3.65052 61.5813 2.71829C63.2207 1.70152 64.974 0.957251 66.8527 0.506417C68.2123 0.180155 69.585 -0.0339423 70.9911 0.00443983C71.7357 0.0247656 72.2266 0.698098 71.8936 1.35352C71.1444 2.82785 70.3509 4.27997 69.5781 5.74249C69.1821 6.49197 68.7959 7.24657 68.398 7.99508C67.1105 10.4174 65.8208 12.8386 64.5296 15.259C63.8397 16.5523 63.1422 17.8416 62.4529 19.1351C61.4158 21.0812 60.3835 23.0298 59.3477 24.9766C58.931 25.7597 58.5009 26.5359 58.0955 27.3247C57.867 27.7692 57.5415 27.9855 57.0261 27.9839C53.698 27.9741 50.3699 27.9707 47.0418 27.9883C46.5791 27.9908 46.2363 27.8347 46.0187 27.4692C45.7831 27.0736 45.9153 26.6836 46.1222 26.2881C46.6253 25.326 47.2088 24.3835 47.5496 23.3638C48.3277 21.0357 47.762 18.9498 46.078 17.1709C44.7052 15.7208 42.972 15.1096 41.0111 15.298C39.1605 15.4757 37.5825 16.2933 36.5468 17.8711C35.8405 18.947 35.2747 20.1148 34.6597 21.2488C33.525 23.3411 32.4124 25.4454 31.2534 27.5243C31.1302 27.7454 30.7609 27.9575 30.5039 27.9596C27.0646 27.9883 23.625 27.9818 20.1855 27.9765C19.4464 27.9753 18.966 27.2365 19.299 26.5571C19.8197 25.4951 20.3919 24.4578 20.9491 23.4136C21.3733 22.6186 21.8128 21.8316 22.2374 21.0368C23.4663 18.7359 24.672 16.4226 25.9261 14.1353C26.5067 13.0762 27.1358 12.0376 27.8194 11.0414C28.6839 9.78151 29.7958 8.74354 30.9857 7.77842C32.0241 6.93618 33.1603 6.2672 34.3602 5.6961C35.7794 5.02067 37.2702 4.58222 38.8242 4.30963C40.1531 4.07653 41.4873 4.01095 42.8189 4.08993C44.748 4.20436 46.614 4.65595 48.4022 5.40215C49.7941 5.983 51.0909 6.72406 52.3056 7.66477Z"
      fill="url(#paint0_linear_756_5339)"
    />
    <Path
      d="M19.6864 56.2841C19.1775 55.8349 18.6701 55.4276 18.2079 54.9748C17.5291 54.3098 16.8778 54.5208 16.4157 55.2065C15.793 56.1306 15.1177 57.0254 14.4025 57.8813C13.2602 59.2483 11.8591 60.3252 10.3454 61.2636C8.69434 62.2871 6.9287 63.0364 5.03677 63.4902C3.6676 63.8186 2.28527 64.0342 0.869251 63.9955C0.119385 63.9751 -0.202984 63.3537 0.132416 62.6939C0.886871 61.2097 1.67848 59.688 2.45673 58.2157C2.85554 57.4613 3.45066 56.5436 3.85132 55.7901C5.14791 53.3516 6.36287 51.1362 7.66319 48.6997C8.35797 47.3978 9.10736 45.8969 9.80156 44.5948C10.846 42.6357 11.8266 40.8636 12.8697 38.9038C13.2892 38.1155 13.4841 37.7386 13.8924 36.9446C14.1224 36.4971 14.4136 35.828 14.9326 35.8295C18.2841 35.8395 21.6357 35.8428 24.9872 35.8251C25.4532 35.8226 25.7984 35.9797 26.0175 36.3476C26.2548 36.7459 26.1216 37.1385 25.9133 37.5366C25.4066 38.5051 24.8191 39.4539 24.4758 40.4804C23.6922 42.824 24.2619 44.9238 25.9578 46.7146C27.3403 48.1744 29.0857 48.7896 31.0604 48.6C32.924 48.4211 34.5132 47.5981 35.5562 46.0098C36.2674 44.9266 36.8372 43.7511 37.4565 42.6096C38.5992 40.5033 39.7197 38.3849 40.8868 36.2922C41.0109 36.0696 41.3828 35.8561 41.6416 35.854C45.1051 35.8251 48.569 35.8316 52.0327 35.837C52.777 35.8382 53.1833 36.4473 52.8479 37.1312C52.3236 38.2003 51.8285 39.0655 51.2674 40.1166C50.8401 40.9169 50.1144 42.2086 49.6868 43.0087C48.3852 45.5277 47.5097 47.2369 46.2468 49.5394C45.6621 50.6055 45.0334 51.8821 44.345 52.885C43.4744 54.1533 42.3547 55.1982 41.1564 56.1697C40.1107 57.0176 38.9665 57.691 37.7581 58.2659C36.329 58.9459 34.8276 59.3872 33.2626 59.6616C31.9244 59.8963 30.5808 59.9623 29.2399 59.8828C27.2972 59.7676 25.4181 59.313 23.6173 58.5618C22.2155 57.9771 20.9096 57.2311 19.6864 56.2841Z"
      fill="url(#paint1_linear_756_5339)"
    />
    <Defs>
      <LinearGradient
        id="paint0_linear_756_5339"
        x1={57.2707}
        y1={40.4898}
        x2={47.3515}
        y2={1.60238}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#04E0BC" />
        <Stop offset={0.684443} stopColor="#05D4DD" />
      </LinearGradient>
      <LinearGradient
        id="paint1_linear_756_5339"
        x1={28.0778}
        y1={30.414}
        x2={35.0035}
        y2={63.9906}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#05D4DD" />
        <Stop offset={0.706514} stopColor="#04E0BC" />
      </LinearGradient>
    </Defs>
  </Svg>
);
export default Logo;
