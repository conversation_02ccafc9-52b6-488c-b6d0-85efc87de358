import * as React from 'react';
import Svg, { Path, Defs, LinearGradient, Stop } from 'react-native-svg';
const YpdLogo = (props) => (
  <Svg
    width={108}
    height={93}
    viewBox="0 0 108 93"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <Path
      d="M78.5859 11.3355C79.3357 11.9955 80.0835 12.5939 80.7646 13.2591C81.7649 14.2361 82.7247 13.9261 83.4057 12.9187C84.3233 11.561 85.3185 10.2466 86.3725 8.98909C88.0558 6.98077 90.1205 5.39879 92.3512 4.0201C94.7843 2.5164 97.3862 1.41569 100.174 0.748946C102.192 0.266433 104.229 -0.0501977 106.316 0.00656612C107.421 0.0366262 108.149 1.03243 107.655 2.00173C106.543 4.18214 105.366 6.32969 104.219 8.49264C103.631 9.60105 103.058 10.717 102.468 11.824C100.557 15.4064 98.6429 18.9872 96.7266 22.5668C95.7028 24.4794 94.6678 26.3861 93.6448 28.2991C92.1056 31.1772 90.5737 34.0591 89.0365 36.9382C88.4182 38.0964 87.7799 39.2443 87.1783 40.4108C86.8392 41.0683 86.3561 41.388 85.5913 41.3858C80.6523 41.3711 75.7132 41.3662 70.7743 41.3922C70.0876 41.3958 69.5789 41.1651 69.2559 40.6246C68.9063 40.0395 69.1025 39.4627 69.4095 38.8778C70.1562 37.4549 71.022 36.0611 71.5278 34.553C72.6826 31.11 71.8431 28.0251 69.3439 25.3943C67.3066 23.2497 64.7345 22.3458 61.8244 22.6244C59.0782 22.8873 56.7363 24.0963 55.1993 26.4298C54.1512 28.021 53.3115 29.748 52.3988 31.4251C50.7148 34.5194 49.0637 37.6315 47.3438 40.7061C47.1609 41.033 46.6128 41.3467 46.2314 41.3498C41.1274 41.3922 36.0229 41.3826 30.9186 41.3747C29.8217 41.373 29.1088 40.2803 29.603 39.2757C30.3757 37.705 31.2249 36.1709 32.0518 34.6267C32.6813 33.4509 33.3336 32.2871 33.9636 31.1115C35.7874 27.7088 37.5768 24.2875 39.4378 20.9049C40.2995 19.3386 41.2332 17.8025 42.2476 16.3293C43.5305 14.466 45.1806 12.9309 46.9464 11.5036C48.4874 10.258 50.1736 9.26863 51.9544 8.42403C54.0604 7.42513 56.2729 6.77669 58.5791 6.37356C60.5512 6.02882 62.5312 5.93183 64.5072 6.04864C67.3702 6.21788 70.1393 6.88574 72.7931 7.9893C74.8588 8.84832 76.7832 9.94428 78.5859 11.3355Z"
      fill="url(#paint0_linear_651_8389)"
    />
    <Path
      d="M30.1772 80.6475C29.422 79.9831 28.669 79.3807 27.9831 78.7111C26.9757 77.7276 26.0091 78.0396 25.3234 79.0538C24.3993 80.4205 23.3971 81.7437 22.3357 83.0096C20.6406 85.0313 18.5613 86.6238 16.3149 88.0117C13.8647 89.5254 11.2444 90.6335 8.43672 91.3047C6.40483 91.7904 4.35341 92.1091 2.25199 92.052C1.13917 92.0217 0.660767 91.1027 1.15851 90.127C2.27814 87.932 3.45292 85.6816 4.60786 83.5042C5.19971 82.3884 6.08289 81.0312 6.67747 79.9169C8.60167 76.3106 10.4047 73.0342 12.3344 69.4308C13.3655 67.5055 14.4776 65.2857 15.5078 63.36C17.0578 60.4627 18.513 57.8418 20.061 54.9435C20.6837 53.7777 20.9728 53.2204 21.5787 52.0461C21.9201 51.3843 22.3522 50.3946 23.1224 50.3969C28.0962 50.4116 33.0701 50.4166 38.0438 50.3904C38.7353 50.3868 39.2476 50.6191 39.5728 51.1632C39.9249 51.7522 39.7273 52.3329 39.4182 52.9216C38.6662 54.354 37.7943 55.7571 37.2849 57.2752C36.122 60.7412 36.9674 63.8466 39.4842 66.495C41.5359 68.6539 44.126 69.5638 47.0567 69.2834C49.8222 69.0188 52.1806 67.8017 53.7285 65.4526C54.7839 63.8508 55.6295 62.1123 56.5486 60.424C58.2445 57.3091 59.9072 54.1762 61.6393 51.0812C61.8235 50.752 62.3754 50.4363 62.7595 50.4331C67.8994 50.3904 73.0399 50.4001 78.1802 50.4081C79.2847 50.4098 79.8877 51.3106 79.3899 52.322C78.6119 53.9032 77.8771 55.1827 77.0444 56.7372C76.4104 57.9208 75.3333 59.831 74.6988 61.0144C72.7672 64.7397 71.4679 67.2674 69.5938 70.6726C68.726 72.2493 67.7929 74.1374 66.7714 75.6205C65.4794 77.4962 63.8177 79.0415 62.0395 80.4783C60.4876 81.7322 58.7895 82.7282 56.9962 83.5784C54.8753 84.584 52.6473 85.2367 50.3248 85.6425C48.3388 85.9896 46.3449 86.0872 44.3549 85.9696C41.4718 85.7993 38.6832 85.127 36.0108 84.016C33.9305 83.1513 31.9925 82.048 30.1772 80.6475Z"
      fill="url(#paint1_linear_651_8389)"
    />
    <Defs>
      <LinearGradient
        id="paint0_linear_651_8389"
        x1={85.9543}
        y1={59.8808}
        x2={71.329}
        y2={2.34556}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#04E0BC" />
        <Stop offset={0.684443} stopColor="#05D4DD" />
      </LinearGradient>
      <LinearGradient
        id="paint1_linear_651_8389"
        x1={42.6303}
        y1={42.3879}
        x2={52.8404}
        y2={92.0586}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#05D4DD" />
        <Stop offset={0.706514} stopColor="#04E0BC" />
      </LinearGradient>
    </Defs>
  </Svg>
);
export default YpdLogo;
