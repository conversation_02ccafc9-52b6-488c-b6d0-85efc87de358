import { createContext, useEffect, useState } from 'react';
import { Appearance } from 'react-native';
import { darkTheme, lightTheme } from '../utils/theme/themes';
import { useSelector } from 'react-redux';

export const ThemeContext = createContext();

export const ThemeProvider = ({ children, initialTheme }) => {
  const [theme, setThemeState] = useState(initialTheme.theme);
  const [themeType, setThemeType] = useState(initialTheme.themeType);
  const systemDefault = useSelector((state) => state.appearance.systemDefault);
  const selectedTheme = useSelector((state) => state.appearance.selectedTheme);

  useEffect(() => {
    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
      const newThemeType = systemDefault
        ? colorScheme === 'dark'
          ? 'dark'
          : 'light'
        : selectedTheme;
      setThemeType(newThemeType);
      setThemeState(newThemeType === 'dark' ? darkTheme : lightTheme);
    });

    return () => subscription.remove();
  }, [theme]);

  const setTheme = (newThemeType) => {
    setThemeType(newThemeType);
    if (newThemeType === 'system') {
      newThemeType = Appearance.getColorScheme();
    }
    setThemeState(newThemeType === 'dark' ? darkTheme : lightTheme);
  };

  return (
    <ThemeContext.Provider
      value={{
        theme,
        themeType,
        setTheme,
        setThemeState,
      }}
    >
      {children}
    </ThemeContext.Provider>
  );
};
