import { createContext, useState, useEffect } from 'react';
import { getCurrentUser, signOut, fetchAuthSession } from 'aws-amplify/auth';
import * as Keychain from 'react-native-keychain';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { persistor } from '../redux/store';
import { useDispatch } from 'react-redux';
import { getUserProfile } from '../api/profile';
import { updateProfile } from '../redux/slices/profileSlice.js';
import { resetState } from '../utils';

export const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const dispatch = useDispatch();

  const [authToken, setAuthToken] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    console.log('[AuthProvider] Starting auth check...');
    const checkAuth = async () => {
      try {
        const credentials = await Keychain.getGenericPassword();
        const storedToken = credentials ? credentials.password : null;

        if (storedToken) {
          console.log(
            '[AuthProvider] Retrieved stored token for user',
            credentials?.username,
          );
          const profileData = await getUserProfile(credentials?.username);
          if (profileData) {
            dispatch(updateProfile(profileData));
            setIsAuthenticated(true);
            setAuthToken(storedToken);
          } else {
            resetState(signOut);
            setIsAuthenticated(false);
          }
          setIsLoading(false);
          return;
        }

        console.log('[AuthProvider] No stored token, checking current user...');
        const user = await getCurrentUser();
        console.log('[AuthProvider] User found:', user);

        const session = await fetchAuthSession();
        const accessToken = session.tokens?.accessToken;
        if (accessToken) {
          console.log('[AuthProvider] Access token from session retrieved.');
          await Keychain.setGenericPassword(
            user.userId,
            accessToken.toString(),
          );
          setAuthToken(accessToken.toString());
          setIsAuthenticated(true);
          console.log('[AuthProvider] Authenticated with fresh token.');

          const profileData = await getUserProfile(user.userId);
          if (profileData) {
            dispatch(updateProfile(profileData));
          } else {
            resetState(signOut);
            setIsAuthenticated(false);
          }
        } else {
          throw new Error('No access token in session');
        }
      } catch (error) {
        console.log(
          '[AuthProvider] No active session or error:',
          error.message,
        );
        setIsAuthenticated(false);
        setAuthToken(null);
      } finally {
        setIsLoading(false);
        console.log('[AuthProvider] Auth check complete.');
      }
    };

    checkAuth();
  }, []);

  const signOutUser = async () => {
    try {
      console.log('[AuthProvider] Starting sign out...');
      await signOut({ global: true });
      console.log('[AuthProvider] Amplify sign out successful.');

      try {
        await fetchAuthSession();
        console.warn('[AuthProvider] Session still exists after sign out!');
      } catch (error) {
        console.log(
          '[AuthProvider] Session cleared as expected:',
          error.message,
        );
      }

      await Keychain.resetGenericPassword();
      console.log(
        '[AuthProvider] Keychain cleared:',
        await Keychain.getGenericPassword(),
      );

      await AsyncStorage.clear();
      console.log(
        '[AuthProvider] AsyncStorage cleared:',
        await AsyncStorage.getAllKeys(),
      );

      await persistor.purge();
      console.log('[AuthProvider] Redux persist state purged.');

      setIsAuthenticated(false);
      setAuthToken(null);
      console.log('[AuthProvider] Authentication state reset.');
    } catch (error) {
      console.error('[AuthProvider] Error during sign out:', error);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        authToken,
        isLoading,
        signOut: signOutUser,
        setIsAuthenticated,
        setAuthToken,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export default AuthProvider;
