import {
  ActivityIndicator,
  Linking,
  SafeAreaView,
  ScrollView,
  View,
  Share,
} from 'react-native';
import Heading from '../../components/Heading';
import styles from '../../assets/styles/Settings.scss';
import ProfileListRender from '../../components/ProfileListRender';
import { Fonts, resetState } from '../../utils';
import MyProfileBanner from '../../assets/svgs/myProfile/MyProfileBanner';
import { CommonActions, useNavigation } from '@react-navigation/native';
import { useState, useContext } from 'react';
import { signOut } from 'aws-amplify/auth';
import { ypdOldGreen } from '../../utils/colors';
import { ThemeContext } from '../../context/ThemeContext';

const Settings = () => {
  const { theme } = useContext(ThemeContext);
  const navigation = useNavigation();

  const [loading, setLoading] = useState(false);

  const handleNotifications = () => {
    navigation.navigate('Notifications');
  };

  const handleManageAccount = () => {
    navigation.navigate('ManageAccount');
  };

  const handleFAQ = () => {
    Linking.openURL('https://yourperfectdose.com/faq-page').catch((err) =>
      console.error('Failed to open URL', err),
    );
  };

  const handleGuides = () => {
    Linking.openURL('https://yourperfectdose.com/guide-page').catch((err) =>
      console.error('Failed to open URL', err),
    );
  };

  const handleReports = () => {
    navigation.navigate('Reports');
  };

  const handleShareApp = () => {
    navigation.navigate('ShareApp');
  };

  const handleContactUs = () => {
    Linking.openURL('https://yourperfectdose.com/contact-us').catch((err) =>
      console.error('Failed to open URL', err),
    );
  };

  const handleBug = () => {
    console.log('Report a Bug Pressed');
  };

  const handleRating = () => {
    console.log('Rate Us Pressed');
  };

  const handleAboutUs = () => {
    navigation.navigate('AboutUs');
  };

  const handleGoals = () => {
    navigation.navigate('Step4', {
      profileBuilding: false,
    });
  };

  const handleCannabisType = () => {
    navigation.navigate('Step6', {
      profileBuilding: false,
      marginTop: 50,
    });
  };

  const handleDosageType = () => {
    navigation.navigate('Step7', {
      profileBuilding: false,
      marginTop: 50,
    });
  };

  const handleLogout = async () => {
    try {
      setLoading(true);
      await resetState(signOut);
      console.log('Logout successful.');
      navigation.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [{ name: 'MainIntro' }],
        }),
      );
    } catch (error) {
      console.error('Error during logout:', error);
    } finally {
      setLoading(false);
    }
  };

  const optionsList = [
    {
      heading: 'Preferences',
      buttons: [
        {
          title: 'Appearance',
          onPress: () => navigation.navigate('Appearance'),
          showIcon: true,
        },
        {
          title: 'Notifications',
          onPress: handleNotifications,
          showIcon: true,
        },
        { title: 'Dosage Type', onPress: handleDosageType, showIcon: true },
        { title: 'Cannabis Type', onPress: handleCannabisType, showIcon: true },
        { title: 'My Goals', onPress: handleGoals, showIcon: true },
        {
          title: 'Manage Account (email/age/sex)',
          onPress: handleManageAccount,
          showIcon: true,
        },
      ],
    },
    {
      heading: 'General',
      buttons: [
        { title: 'FAQ', onPress: handleFAQ, showIcon: true },
        { title: 'Guides', onPress: handleGuides, showIcon: true },
        { title: 'User Reports', onPress: handleReports, showIcon: true },
        { title: 'Share App', onPress: handleShareApp, showIcon: true },
      ],
    },
    {
      heading: 'Support',
      buttons: [
        { title: 'Contact us', onPress: handleContactUs, showIcon: true },
        // { title: 'Report a bug', onPress: handleBug, showIcon: true },
        { title: 'About Us', onPress: handleAboutUs, showIcon: true },
        // { title: 'Rate us', onPress: handleRating, showIcon: false },
        { title: 'Logout', onPress: handleLogout, showIcon: false },
      ],
    },
  ];

  return (
    <SafeAreaView
      style={[
        styles.safeAreaView,
        { backgroundColor: theme.colors.background },
      ]}
    >
      <ScrollView>
        <View style={styles.screen}>
          <View style={styles.headerContainer}>
            <Heading
              text="Settings"
              fontSize={24}
              fontFamily={Fonts.REGULAR}
              color={theme.colors.text}
            />
          </View>
          <View style={styles.myProfile}>
            <MyProfileBanner />
          </View>

          <View
            style={[
              styles.buttonContainer,
              { backgroundColor: theme.colors.background },
            ]}
          >
            {optionsList.map((section, index) => (
              <View key={index}>
                <ProfileListRender
                  text={section.heading}
                  listOfButtons={section.buttons}
                  padding={[0, 0, 0, 10]}
                  textColor={theme.colors.text}
                />
                <View
                  style={[
                    styles.divider,
                    { backgroundColor: theme.colors.border },
                  ]}
                />
              </View>
            ))}
          </View>
        </View>
      </ScrollView>
      {loading && (
        <View
          style={[
            styles.loadingOverlay,
            { backgroundColor: theme.colors.background + '80' },
          ]}
        >
          <ActivityIndicator size="large" color={ypdOldGreen} />
        </View>
      )}
    </SafeAreaView>
  );
};

export default Settings;
