import {
  View,
  TouchableOpacity,
  Alert,
  Platform,
  FlatList,
  SafeAreaView,
} from 'react-native';
import { Card } from 'react-native-paper';
import { useCallback, useContext, useState } from 'react';
import RNFS from 'react-native-fs';
import { PermissionsAndroid } from 'react-native';
import { getUrl } from 'aws-amplify/storage';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import FileViewer from 'react-native-file-viewer';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useSelector } from 'react-redux';
import styles from '../../assets/styles/Reports.scss';
import ProfileBuilderButton from '../../components/ProfileBuilderButton';
import { ThemeContext } from '../../context/ThemeContext';
import { ypdGreen, ypdTeal, ypdWhite } from '../../utils/colors';
import LinearGradient from 'react-native-linear-gradient';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Heading from '../../components/Heading';
import CustomButton from '../../components/CustomButton';
import { createUserReport, listUserReports } from '../../api/report';
import { recordEvent } from '../../api/events';
import { Fonts } from '../../utils';

const Reports = () => {
  const REPORT_GENERATION_DAYS = process.env.REPORT_GENERATION_DAYS || 7;

  const { theme } = useContext(ThemeContext);
  const navigation = useNavigation();
  const gradientColors = [ypdTeal, ypdGreen];

  const [reports, setReports] = useState([]);
  const [loading, setLoading] = useState(true);
  const [generateNewReport, setGenerateNewReport] = useState(true);
  const profile = useSelector((state) => state.profile);
  const username = profile?.username;

  const [screenTime, setScreenTime] = useState(null);

  useFocusEffect(
    useCallback(() => {
      fetchReports();
      setScreenTime(new Date());
    }, []),
  );

  const fetchReports = async () => {
    let sortedReports = [];
    const createdAt = new Date().toISOString();
    let attributes = {
      currentScreen: 'reports',
    };
    try {
      const reports = await listUserReports(username);
      const filteredReports = reports.filter((report) => report?.reportURL);
      sortedReports = filteredReports.sort(
        (a, b) => new Date(b.createdAt) - new Date(a.createdAt),
      );
      if (sortedReports.length > 0) {
        const latestReportDate = new Date(sortedReports[0].createdAt);
        const XDaysAgo = new Date();
        XDaysAgo.setDate(XDaysAgo.getDate() - REPORT_GENERATION_DAYS);
        if (latestReportDate >= XDaysAgo) {
          setGenerateNewReport(false);
        } else {
          setGenerateNewReport(true);
        }
      }
      await recordEvent(
        'Reports Event',
        username,
        true,
        '#R1',
        attributes,
        createdAt,
      );
    } catch (error) {
      attributes['error'] = String(error);
      recordEvent(
        'Reports Event',
        username,
        false,
        '#R2',
        attributes,
        createdAt,
      );
      console.error('Error fetching reports:', error);
    } finally {
      setLoading(false);
      setReports(sortedReports);
    }
    return sortedReports;
  };

  const generateReport = async () => {
    const createdAt = new Date().toISOString();
    let attributes = {
      currentScreen: 'reports',
      screenTime: (new Date() - screenTime) / 1000,
    };
    setScreenTime(new Date());
    try {
      setLoading(true);
      setGenerateNewReport(false);
      await createUserReport(username);
      await recordEvent(
        'Reports Event',
        username,
        true,
        '#R3',
        attributes,
        createdAt,
      );
      Alert.alert(
        'Success',
        'Your report is being generated. Please wait a while.',
      );
      const interval = setInterval(async () => {
        try {
          const initialCount = reports?.length || 0;
          const updatedReports = await fetchReports();
          const updatedCount = updatedReports?.length || 0;
          if (updatedCount > initialCount) {
            clearInterval(interval);
            Alert.alert('Report Ready', 'Your report is now available.');
          }
        } catch (pollError) {
          console.error('Error while polling reports:', pollError);
        }
      }, 5000);
    } catch (error) {
      attributes['error'] = String(error);
      await recordEvent(
        'Reports Event',
        username,
        false,
        '#R4',
        attributes,
        createdAt,
      );
      Alert.alert(
        'Error',
        'An error occurred while generating your report. Please try again.',
      );
      setGenerateNewReport(true);
    } finally {
      setLoading(false);
    }
  };

  const downloadReport = async (fileName) => {
    const createdAt = new Date().toISOString();
    let attributes = {
      currentScreen: 'reports',
      screenTime: (new Date() - screenTime) / 1000,
    };
    setScreenTime(new Date());
    try {
      setLoading(true);
      const permissionGranted = await requestStoragePermission();
      if (!permissionGranted) {
        Alert.alert(
          'Permission Denied',
          'Storage access is required to download files.',
        );
        return;
      }

      const getUrlResult = await getUrl({
        path: `user_reports/${username}/${fileName}`,
        options: {
          expiresIn: 60,
        },
      });

      const signedUrl = getUrlResult.url;

      const localPath =
        Platform.OS === 'android'
          ? `${RNFS.DownloadDirectoryPath}/${fileName}`
          : `${RNFS.DocumentDirectoryPath}/${fileName}`;

      const downloadResult = await RNFS.downloadFile({
        fromUrl: String(signedUrl).trim(),
        toFile: localPath,
      }).promise;

      if (downloadResult.statusCode === 200) {
        console.log('File saved successfully at:', localPath);
        FileViewer.open(localPath)
          .then(() => {
            console.log('File opened successfully.');
          })
          .catch((error) => {
            console.error('Error opening file:', error);
            Alert.alert('Error', 'Failed to open the file');
          });
        recordEvent(
          'Reports Event',
          username,
          true,
          '#R5',
          attributes,
          createdAt,
        );
      } else {
        console.error('Download failed:', downloadResult.statusCode);
        attributes['error'] = downloadResult;
        recordEvent(
          'Reports Event',
          username,
          false,
          '#R6',
          attributes,
          createdAt,
        );
        Alert.alert('Error', 'Failed to download the report.');
      }
    } catch (error) {
      console.error('Error downloading report:', error);
      Alert.alert('Error', 'An error occurred while downloading the report.');
      attributes['error'] = String(error);
      recordEvent(
        'Reports Event',
        username,
        false,
        '#R7',
        attributes,
        createdAt,
      );
    } finally {
      setLoading(false);
    }
  };

  const requestStoragePermission = async () => {
    const createdAt = new Date().toISOString();
    let attributes = {
      currentScreen: 'reports',
      screenTime: (new Date() - screenTime) / 1000,
    };
    setScreenTime(new Date());
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
          {
            title: 'Storage Permission',
            message: 'YPD needs access to your storage to download reports.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          },
        );
        attributes['granted'] = String(granted);
        recordEvent(
          'Reports Event',
          username,
          true,
          '#R8',
          attributes,
          createdAt,
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } catch (err) {
        console.warn(err);
        attributes['error'] = String(err);
        recordEvent(
          'Reports Event',
          username,
          false,
          '#R9',
          attributes,
          createdAt,
        );
        return false;
      }
    }
    return true;
  };

  return (
    <SafeAreaView
      style={[styles.mainWrapper, { backgroundColor: theme.colors.background }]}
    >
      <View style={{ flex: 1 }}>
        <View style={{ alignItems: 'center' }}>
          <LinearGradient
            colors={gradientColors}
            style={{
              width: 80,
              height: 80,
              borderRadius: 40,
              justifyContent: 'center',
              alignItems: 'center',
              marginBottom: 10,
            }}
          >
            <MaterialIcons name="file-copy" size={40} color={ypdWhite} />
          </LinearGradient>

          <Heading
            text="Your Reports"
            size="md"
            fontFamily={Fonts.BOLD}
            style={{ color: theme.colors.text }}
          />
        </View>
        {reports.length === 0 && (
          <View>
            <View
              style={[
                styles.shadowLine,
                { backgroundColor: theme.colors.reportText },
              ]}
            />
            <View style={styles.emptyStateWrapper}>
              <Heading
                text="No reports."
                size="sm"
                fontFamily={Fonts.BOLD}
                style={[styles.emptyStateTitle, { color: theme.colors.text }]}
              />
              <Heading
                text="You haven’t generated any report yet."
                size="xs"
                fontFamily={Fonts.REGULAR}
                style={[
                  styles.emptyStateDescription,
                  { color: theme.colors.text },
                ]}
              />
            </View>
          </View>
        )}
        <FlatList
          data={reports}
          keyExtractor={(item) => item.id}
          removeClippedSubviews={false}
          contentContainerStyle={{
            paddingHorizontal: 10,
            paddingBottom: 20,
            flexGrow: 1,
          }}
          style={{ flex: 1 }}
          renderItem={({ item }) => (
            <View style={{ marginVertical: 5 }}>
              <Card
                style={[
                  styles.card,
                  { backgroundColor: theme.colors.lightTextGray },
                ]}
              >
                <TouchableOpacity onPress={() => downloadReport(item.name)}>
                  <View style={styles.cardContent}>
                    <Icon
                      name="description"
                      size={30}
                      color={theme.colors.reportText}
                    />
                    <Heading
                      text={item.name}
                      size="sm"
                      fontFamily={Fonts.REGULAR}
                      style={[styles.filename, { color: theme.colors.text }]}
                    />
                  </View>
                </TouchableOpacity>
              </Card>
            </View>
          )}
        />
        <View style={styles.innerContainer}>
          <View style={styles.buttonContainer}>
            <CustomButton
              title="Generate new report"
              onPress={generateReport}
              size={20}
              fontSize={17}
              variant="newGreen"
              width="100%"
              disabled={loading || !generateNewReport}
              activityIndicator={loading}
            />
          </View>
          {!generateNewReport && (
            <Heading
              text={`A report can be generated once every ${REPORT_GENERATION_DAYS} days.`}
              size="xs"
              fontFamily={Fonts.REGULAR}
              style={[styles.message, { color: theme.colors.text }]}
            />
          )}
        </View>
      </View>
      <View style={styles.bottomButtonContainer}>
        <ProfileBuilderButton
          backDisabled={loading}
          onlyBackButton={true}
          onBackPress={() => navigation.goBack()}
        />
      </View>
    </SafeAreaView>
  );
};

export default Reports;
