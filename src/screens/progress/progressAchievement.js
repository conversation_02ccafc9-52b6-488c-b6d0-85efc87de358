import { View, ScrollView } from 'react-native';
import { useContext, useEffect, useRef, useState } from 'react';
import CircularTileSlider from '../../components/CircularTileSlider';
import { Fonts } from '../../utils';
import Heading from '../../components/Heading';
import Thunder from '../../assets/svgs/checkin/Thunder';
import DoubleTick from '../../assets/svgs/checkin/DoubleTick';
import styles from '../../assets/styles/ProgressAchievement.scss';
import CalendarComponent from '../../components/CalendarComponent';
import { useSelector } from 'react-redux';
import { ThemeContext } from '../../context/ThemeContext';

const ProgressAchievement = ({ currentDate, setCurrentDate }) => {
  const { theme } = useContext(ThemeContext);
  const scrollRef = useRef(null);
  const [viewMode, setViewMode] = useState('week');

  const { checkinStats = {} } = useSelector((state) => state.suggestions || {});
  const { dailyCheckins, weeklyStreaks, fullStreakWeeks } = checkinStats;

  useEffect(() => {
    if (viewMode === 'month' && scrollRef.current) {
      setTimeout(() => {
        scrollRef.current.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [viewMode]);

  return (
    <ScrollView
      style={{ flex: 1, backgroundColor: theme.colors.background }}
      contentContainerStyle={{ flexGrow: 1 }}
      ref={scrollRef}
    >
      <CircularTileSlider
        streakCount={weeklyStreaks}
        title="Progress Achievement"
      />

      <View style={[styles.dailyWeeklyContainer]}>
        <View style={styles.dailyWeekly}>
          <DoubleTick />
          <Heading
            text={dailyCheckins}
            size="md"
            fontFamily={Fonts.MEDIUM}
            color={theme.colors.text}
          />
          <Heading
            text="Daily check-ins"
            size="small"
            color={theme.colors.placeholderTextColor}
          />
        </View>

        <View style={styles.dailyWeekly}>
          <Thunder />
          <Heading
            text={fullStreakWeeks}
            size="md"
            fontFamily={Fonts.MEDIUM}
            color={theme.colors.text}
          />
          <Heading
            text="Weekly streaks"
            size="small"
            color={theme.colors.placeholderTextColor}
          />
        </View>
      </View>

      <View
        style={[
          styles.horizontalDivider,
          { backgroundColor: theme.colors.lightTextGray },
        ]}
      />
      <View style={styles.weeklyCalendarView}>
        <CalendarComponent
          initialDate={currentDate}
          onDateChange={setCurrentDate}
          initialViewMode={viewMode}
          isLocked={true}
          onViewModeChange={setViewMode}
        />
      </View>
    </ScrollView>
  );
};

export default ProgressAchievement;
