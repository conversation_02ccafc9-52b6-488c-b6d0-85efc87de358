import { useContext, useState } from 'react';
import { View, SafeAreaView } from 'react-native';
import ProgressActivity from './progressActivity';
import ProgressAchievement from './progressAchievement';
import CustomButton from '../../components/CustomButton';
import { Fonts } from '../../utils';
import { ypdBlack } from '../../utils/colors';
import styles from '../../assets/styles/ProgressActivity.scss';
import { ThemeContext } from '../../context/ThemeContext';

const BUTTONS = [
  { title: 'Achievements', width: 103 },
  { title: 'Activity', width: 68 },
];

const ProgressScreen = () => {
  const { theme } = useContext(ThemeContext);
  const isDarkMode = theme;
  const [activeButton, setActiveButton] = useState('Achievements');
  const [currentDate, setCurrentDate] = useState(
    new Date().toISOString().split('T')[0],
  );

  const handleToggle = (button) => setActiveButton(button);

  const renderToggleButtons = () =>
    BUTTONS.map(({ title, width }) => {
      const isActive = activeButton === title;
      return (
        <CustomButton
          key={title}
          title={title}
          variant="transparent"
          fontFamily={Fonts.MEDIUM}
          color={
            isActive
              ? isDarkMode
                ? theme.colors.text || '#D3D3D3'
                : ypdBlack
              : theme.colors.text
          }
          style={isActive ? [styles.borderButtons] : []}
          height={30}
          width={width}
          alignSelf="flex-end"
          onPress={() => handleToggle(title)}
        />
      );
    });

  const renderContent = () => {
    const Component =
      activeButton === 'Activity' ? ProgressActivity : ProgressAchievement;
    return (
      <Component currentDate={currentDate} setCurrentDate={setCurrentDate} />
    );
  };

  return (
    <SafeAreaView
      style={[styles.safeArea, { backgroundColor: theme.colors.lightTextGray }]}
    >
      <View style={styles.container}>
        <View style={styles.toggleContainer}>{renderToggleButtons()}</View>
        {renderContent()}
      </View>
    </SafeAreaView>
  );
};

export default ProgressScreen;
