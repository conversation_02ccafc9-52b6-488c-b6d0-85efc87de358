import { useEffect, useRef, useState, useContext } from 'react';
import { View, Animated, Dimensions, StyleSheet } from 'react-native';
import styles from '../../assets/styles/WarningScreen';
import { useNavigation } from '@react-navigation/native';
import Alert from '../../assets/svgs/profileBuilder/Alert';
import Heading from '../../components/Heading';
import { ypdBlack, ypdWhite } from '../../utils/colors';
import { Fonts } from '../../utils';
import { ThemeContext } from '../../context/ThemeContext';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

const WarningScreen = ({
  message,
  fromGeofencing = false,
  buttonText,
  onButtonPress,
}) => {
  const { theme } = useContext(ThemeContext);
  const growth = useRef(new Animated.Value(0)).current;
  const backgroundColor = useRef(new Animated.Value(0)).current;
  const navigation = useNavigation();
  const [showAlert, setShowAlert] = useState(true);

  useEffect(() => {
    if (fromGeofencing) return;

    const timeout = setTimeout(() => {
      setShowAlert(false);
      Animated.parallel([
        Animated.timing(growth, {
          toValue: 1,
          duration: 3000,
          useNativeDriver: true,
        }),
        Animated.timing(backgroundColor, {
          toValue: 1,
          duration: 3000,
          useNativeDriver: false,
        }),
      ]).start(() => {
        requestAnimationFrame(() => {
          navigation.navigate('CloseAppWarning');
        });
      });
    }, 3000);

    return () => clearTimeout(timeout);
  }, [fromGeofencing, navigation]);

  if (fromGeofencing) {
    return (
      <View
        style={[styles.fullScreenContainer, { backgroundColor: '#18F1C7' }]}
      >
        <View style={styles.centeredContent}>
          <View style={styles.iconContainer}>
            <View style={[animationStyles.triangle, { zIndex: 0 }]} />
            <View style={{ zIndex: 1 }}>
              <Alert fill="#FFFFFF" />
            </View>
          </View>

          <Heading
            text={
              message ||
              "Your well-being is our top priority. To ensure safety, we're unable to offer our services."
            }
            size="lg"
            color={ypdBlack}
            fontFamily={Fonts.REGULAR}
            style={styles.title}
          />
          <Heading
            text="Stay safe and healthy!"
            size="sm"
            color={ypdBlack}
            fontFamily={Fonts.REGULAR}
          />
          {buttonText && (
            <Heading
              text={buttonText}
              size="md"
              color={ypdWhite}
              fontFamily={Fonts.REGULAR}
              onPress={onButtonPress}
              style={animationStyles.topGap}
            />
          )}
        </View>
      </View>
    );
  }

  return (
    <Animated.View
      style={[
        styles.fullScreenContainer,
        { backgroundColor: theme.colors.background },
      ]}
    >
      <View style={styles.centeredContent}>
        <Animated.View
          style={[
            styles.iconContainer,
            {
              transform: [
                {
                  scale: growth.interpolate({
                    inputRange: [0, 0.2, 1],
                    outputRange: [
                      1,
                      1,
                      Math.max(SCREEN_WIDTH / 127, SCREEN_HEIGHT / 113) * 3,
                    ],
                  }),
                },
              ],
            },
          ]}
        >
          {showAlert && <Alert />}
          <Animated.View
            style={[
              animationStyles.triangle,
              {
                opacity: growth.interpolate({
                  inputRange: [0, 0.2, 1],
                  outputRange: [0, 1, 1],
                }),
              },
            ]}
          />
        </Animated.View>

        <Animated.Text
          style={[
            styles.title,
            { color: theme.colors.text },
            {
              opacity: growth.interpolate({
                inputRange: [0, 0.2, 1],
                outputRange: [1, 1, 0],
              }),
            },
          ]}
        >
          {message ||
            "Your well-being is our top priority. To ensure safety, we're unable to offer our services."}
        </Animated.Text>

        <Animated.Text
          style={[
            styles.subtext,
            { color: theme.colors.text },
            {
              opacity: growth.interpolate({
                inputRange: [0, 0.2, 1],
                outputRange: [1, 1, 0],
              }),
            },
          ]}
        >
          Stay safe and healthy!
        </Animated.Text>
      </View>
    </Animated.View>
  );
};

const animationStyles = StyleSheet.create({
  triangle: {
    width: 0,
    height: 0,
    borderLeftWidth: 63.5,
    borderRightWidth: 63.5,
    borderBottomWidth: 113,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: '#18F1C7',
    position: 'absolute',
    top: 0,
    left: '50%',
    transform: [{ translateX: -63.5 }],
  },
  topGap: {
    marginTop: 20,
  },
});

export default WarningScreen;
