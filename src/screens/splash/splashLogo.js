import { useEffect, useRef } from 'react';
import { View, Animated, Easing, StyleSheet, Dimensions } from 'react-native';
import { ypdTeal, ypdWhite } from '../../utils/colors';
import LogoUper from '../../assets/svgs/profileBuilder/LogoUper';
import LogoLower from '../../assets/svgs/profileBuilder/LogoLower';

const AnimatedLogoUper = Animated.createAnimatedComponent(LogoUper);
const AnimatedLogoLower = Animated.createAnimatedComponent(LogoLower);

const SplashLogo = () => {
  const screenHeight = Dimensions.get('window').height;

  const topPart = useRef(new Animated.Value(-screenHeight)).current;
  const bottomPart = useRef(new Animated.Value(screenHeight)).current;
  const scaleAnim = useRef(new Animated.Value(1.5)).current;
  const textOpacity = useRef(new Animated.Value(0)).current;
  const textScale = useRef(new Animated.Value(1.5)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(topPart, {
        toValue: -60,
        duration: 1200,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(bottomPart, {
        toValue: -130,
        duration: 1200,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 800,
        easing: Easing.out(Easing.ease),
        useNativeDriver: true,
      }),
      Animated.timing(textScale, {
        toValue: 1,
        duration: 800,
        delay: 300,
        easing: Easing.out(Easing.exp),
        useNativeDriver: true,
      }),
      Animated.timing(textOpacity, {
        toValue: 1,
        duration: 800,
        delay: 400,
        easing: Easing.inOut(Easing.ease),
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  return (
    <View style={styles.container}>
      <AnimatedLogoUper
        style={[
          styles.logotop,
          { transform: [{ translateY: topPart }, { scale: scaleAnim }] },
        ]}
        width={80}
        height={80}
      />
      <Animated.Text
        style={[
          styles.text,
          {
            opacity: textOpacity,
            transform: [{ scale: textScale }],
          },
        ]}
      >
        Your Perfect Dose
      </Animated.Text>
      <AnimatedLogoLower
        style={[
          styles.logoBottom,
          { transform: [{ translateY: bottomPart }, { scale: scaleAnim }] },
        ]}
        width={80}
        height={80}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: ypdTeal,
  },
  text: {
    fontSize: 32,
    fontWeight: 'bold',
    color: ypdWhite,
    marginVertical: 25,
  },
  logotop: {
    marginVertical: -25,
  },
  logoBottom: {
    marginVertical: -25,
    marginRight: 50,
  },
});

export default SplashLogo;
