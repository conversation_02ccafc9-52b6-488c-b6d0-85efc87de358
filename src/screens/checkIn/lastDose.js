import {
  SafeAreaView,
  View,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native';
import Heading from '../../components/Heading';
import { Fonts } from '../../utils';
import styles from '../../assets/styles/LastDose';
import Dose from '../../components/Dose';
import { ThemeContext } from '../../context/ThemeContext';
import { useContext } from 'react';

const LastDose = () => {
  const { theme } = useContext(ThemeContext);

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <ScrollView
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ flexGrow: 1 }}
          >
            <View style={styles.header}>
              <Heading
                text="If you are currently taking anything else, please let us know"
                size="lg"
                fontFamily={Fonts.MEDIUM}
                color={theme.colors.text}
              />
            </View>
            <View style={styles.innerWrapper}>
              <Dose isCheckin={true} />
            </View>
          </ScrollView>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default LastDose;
