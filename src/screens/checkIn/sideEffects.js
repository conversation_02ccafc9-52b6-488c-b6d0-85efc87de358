import { useEffect, useState, useCallback, useContext } from 'react';
import {
  SafeAreaView,
  TouchableOpacity,
  View,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  ScrollView,
} from 'react-native';
import styles from '../../assets/styles/SideEffects';
import Input from '../../components/Input';
import { useDispatch, useSelector } from 'react-redux';
import CheckinHeader from '../../components/CheckinHeader';
import Heading from '../../components/Heading';
import { ypdOldGreen, ypdTextGrey, ypdWhite } from '../../utils/colors';
import { Fonts, sideEffectOptions } from '../../utils';
import WarningModal from '../profileBuilding/warningModal';
import ProfileBuilderButton from '../../components/ProfileBuilderButton';
import {
  setCustomSideEffects,
  setSideEffects,
} from '../../redux/slices/checkinSlice';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { resetLoadingState } from '../../utils';
import { recordEvent } from '../../api/events';
import { ThemeContext } from '../../context/ThemeContext';

const SideEffects = () => {
  const { theme } = useContext(ThemeContext);
  const dispatch = useDispatch();
  const navigation = useNavigation();

  const sideEffects = useSelector((state) => state.checkIn.sideEffects) || [];
  const customSideEffects =
    useSelector((state) => state.checkIn.customSideEffects) || [];

  const [selectedBlocks, setSelectedBlocks] = useState(
    sideEffects.filter((item) => sideEffectOptions.includes(item)),
  );
  const [screenTime, setScreenTime] = useState(null);
  const [customInput, setCustomInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [warningMessage, setWarningMessage] = useState('');
  const [isRashWarning, setIsRashWarning] = useState(false);
  const [warningModalVisible, setWarningModalVisible] = useState(false);
  const [customProblems, setCustomProblems] = useState([...customSideEffects]);

  const profileData = useSelector((state) => state.profile);

  resetLoadingState(setIsLoading);

  useEffect(() => {
    const allSideEffects = [...selectedBlocks, ...customProblems];
    dispatch(setSideEffects(allSideEffects));
    dispatch(setCustomSideEffects(customProblems));
  }, [selectedBlocks, customProblems, dispatch]);

  useFocusEffect(
    useCallback(() => {
      setScreenTime(new Date());
    }, []),
  );

  const saveDosingEvent = async () => {
    const createdAt = new Date().toISOString();
    const attributes = {
      currentScreen: 'sideEffects',
      screenTime: (new Date() - screenTime) / 1000,
    };
    await recordEvent(
      'Dosing Event',
      profileData.username,
      true,
      '#D3',
      attributes,
      createdAt,
    );
  };

  const totalSelections = selectedBlocks.length + customProblems.length;

  const handleSelection = async (item) => {
    const alreadySelected = selectedBlocks.includes(item);

    if (alreadySelected) {
      setSelectedBlocks(selectedBlocks.filter((i) => i !== item));
    } else if (totalSelections < 2) {
      setSelectedBlocks([...selectedBlocks, item]);

      if (item === 'Skin Rash*') {
        setWarningMessage(
          'If you experience rash, call 911 or go to the nearest emergency room immediately.',
        );
        setIsRashWarning(true);
        setWarningModalVisible(true);
      }
    } else {
      setWarningMessage('You can only select up to 2 problems.');
      setIsRashWarning(false);
      setWarningModalVisible(true);
    }
  };

  const handleInputChange = (text) => {
    setCustomInput(text);
  };

  const handleSubmitEditing = () => {
    const trimmed = customInput.trim();
    if (!trimmed) return;

    if (
      trimmed.toLowerCase() === 'skin rash*' ||
      trimmed.toLowerCase() === 'rash'
    ) {
      if (totalSelections < 2) {
        setSelectedBlocks([...selectedBlocks, 'Skin Rash*']);
        setWarningMessage(
          'If you experience rash, call 911 or go to the nearest emergency room immediately.',
        );
        setIsRashWarning(true);
        setWarningModalVisible(true);
      } else {
        setWarningMessage('You can only select up to 2 problems.');
        setIsRashWarning(false);
        setWarningModalVisible(true);
      }
      setCustomInput('');
      return;
    }

    if (totalSelections >= 2) {
      setWarningMessage('You can only select up to 2 problems.');
      setIsRashWarning(false);
      setWarningModalVisible(true);
      setCustomInput('');
      return;
    }

    if (sideEffectOptions.includes(trimmed)) {
      if (!selectedBlocks.includes(trimmed)) {
        setSelectedBlocks([...selectedBlocks, trimmed]);
      }
      setCustomInput('');
      Keyboard.dismiss();
      return;
    }

    if (customProblems.includes(trimmed)) {
      setCustomInput('');
      Keyboard.dismiss();
      return;
    }

    setCustomProblems([...customProblems, trimmed]);
    setCustomInput('');
    Keyboard.dismiss();
  };

  const handleCustomProblemPress = (item) => {
    setCustomProblems(customProblems.filter((i) => i !== item));
  };

  const hasRash = selectedBlocks.includes('Skin Rash*');
  const hasOtherProblem =
    totalSelections > 1 || (hasRash && customProblems.length > 0);
  const buttonDisabled =
    totalSelections < 1 || (hasRash && hasOtherProblem) || isLoading;

  const buttonStyle = {
    backgroundColor: buttonDisabled ? ypdWhite : ypdOldGreen,
    border: buttonDisabled ? '1px solid ypd-dark-grey' : 'none',
    color: buttonDisabled ? 'ypd-dark-grey' : 'ypd-black',
  };

  const handleNext = async () => {
    setIsLoading(true);
    await saveDosingEvent();
    navigation.navigate('OverallSideEffect');
  };

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
    >
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      >
        <TouchableWithoutFeedback
          onPress={() => {
            Keyboard.dismiss();
          }}
        >
          <ScrollView
            contentContainerStyle={{ flexGrow: 1, justifyContent: 'center' }}
            keyboardShouldPersistTaps="handled"
            pointerEvents={isLoading ? 'none' : 'auto'}
          >
            <View style={styles.headingContainer}>
              <CheckinHeader
                text="Let us know what's bothering you the most?"
                textAlign="left"
                alignImage="center"
              />
            </View>
            <View style={styles.container}>
              <View style={styles.symptomsContainer}>
                {sideEffectOptions.map((item, index) => (
                  <TouchableOpacity
                    style={[
                      styles.blockContainer,
                      selectedBlocks.includes(item)
                        ? theme.dark
                          ? styles.nonHighlightedBlock
                          : styles.highlightedBlock
                        : null,
                    ]}
                    key={index}
                    onPress={() => handleSelection(item)}
                    disabled={isLoading}
                  >
                    <Heading
                      text={item}
                      fontSize={18}
                      color={theme.colors.darkTextGray}
                      fontFamily={Fonts.MEDIUM}
                    />
                  </TouchableOpacity>
                ))}

                {customProblems.map((item, index) => (
                  <TouchableOpacity
                    style={[
                      styles.blockContainer,
                      theme.dark
                        ? styles.nonHighlightedBlock
                        : styles.highlightedBlock,
                    ]}
                    key={`custom-${index}`}
                    onPress={() => handleCustomProblemPress(item)}
                    disabled={isLoading}
                  >
                    <Heading
                      text={item}
                      fontSize={18}
                      color={theme.colors.darkTextGray}
                      fontFamily={Fonts.MEDIUM}
                    />
                  </TouchableOpacity>
                ))}
              </View>
              <Input
                value={customInput}
                onChangeText={handleInputChange}
                placeholder="Didn't see yours? type it here"
                onSubmitEditing={handleSubmitEditing}
                returnKeyType="done"
                borderRadius={21}
                hasValue={!!customInput}
                disabled={isLoading}
              />
            </View>
            <View style={styles.sideEffectContainer}>
              <ProfileBuilderButton
                continueText="Next"
                width="30%"
                backDisabled={isLoading}
                activityIndicator={isLoading}
                continueDisabled={buttonDisabled}
                continueButtonStyle={buttonStyle}
                onContinuePress={handleNext}
                onBackPress={() => navigation.navigate('IndividualEffects')}
              />
            </View>
            <WarningModal
              visible={warningModalVisible}
              onClose={() => {
                setWarningModalVisible(false);
                if (isRashWarning) {
                  setSelectedBlocks([]);
                  setCustomProblems([]);
                  navigation.navigate('ConfirmRash');
                }
              }}
              text={warningMessage}
              icon="alert"
              size={30}
              iconColor="red"
            />
          </ScrollView>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default SideEffects;
