import { <PERSON><PERSON>rea<PERSON><PERSON><PERSON>, ScrollView, View } from 'react-native';
import Heading from '../../components/Heading';
import {
  Fonts,
  responsiveStyles,
  useName,
  resetLoadingState,
} from '../../utils';
import styles from '../../assets/styles/OverallEffect.scss';
import ProfileBuilderButton from '../../components/ProfileBuilderButton';
import { useCallback, useEffect, useState, useContext } from 'react';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import CircularSlider from '../../components/CircularSlider';
import { setOverallEffect } from '../../redux/slices/checkinSlice';
import { useDispatch, useSelector } from 'react-redux';
import { recordEvent } from '../../api/events';
import { ThemeContext } from '../../context/ThemeContext';

const OverallEffect = () => {
  const { theme } = useContext(ThemeContext);
  const name = useName();
  const dispatch = useDispatch();
  const navigation = useNavigation();

  const indexSelected = useSelector((state) => state.checkIn.overallEffect);
  const [selectedIndex, setSelectedIndex] = useState(indexSelected);

  const [screenTime, setScreenTime] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const profileData = useSelector((state) => state.profile);

  useEffect(() => {
    dispatch(setOverallEffect(selectedIndex));
  }, [selectedIndex]);

  useFocusEffect(
    useCallback(() => {
      setScreenTime(new Date());
    }, []),
  );

  resetLoadingState(setIsLoading);

  const saveDosingEvent = async () => {
    const createdAt = new Date().toISOString();
    const attributes = {
      isTriggered: true,
      currentScreen: 'overallEffect',
      screenTime: (new Date() - screenTime) / 1000,
    };
    await recordEvent(
      'Dosing Event',
      profileData.username,
      true,
      '#D1',
      attributes,
      createdAt,
    );
  };

  const handleContinuePress = async () => {
    setIsLoading(true);
    await saveDosingEvent();
    navigation.navigate('IndividualEffects');
  };

  return (
    <SafeAreaView
      style={[styles.safeArea, { backgroundColor: theme.colors.background }]}
    >
      <View style={styles.container}>
        <View pointerEvents={isLoading ? 'none' : 'auto'}>
          <CircularSlider
            value={selectedIndex}
            onChange={setSelectedIndex}
            heading={`Hi ${name ? name : 'user'}!`}
            subheading="How are you feeling?"
            width={responsiveStyles.sliderWidth}
          />
        </View>
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={styles.buttonContainer}>
            <ProfileBuilderButton
              continueText="Next"
              backDisabled={isLoading}
              activityIndicator={isLoading}
              continueDisabled={selectedIndex === 0 || isLoading}
              onContinuePress={handleContinuePress}
              continueButtonStyle={styles.continueButton}
              onBackPress={() =>
                navigation.reset({
                  index: 0,
                  routes: [
                    {
                      name: 'Tabs',
                      state: {
                        routes: [{ name: 'HomeScreen' }],
                      },
                    },
                  ],
                })
              }
            />
          </View>
          <View
            style={[
              styles.bottomContainer,
              { backgroundColor: theme.colors.lightTextGray },
            ]}
          >
            <Heading
              text="A Complete Approach:"
              size="md"
              fontFamily={Fonts.MEDIUM}
              color={theme.colors.text}
            />
            <Heading
              text="We treat symptoms as part of a connected system, not in isolation. Our holistic dosing approach helps you find balance by addressing the full picture of your health."
              size="sm"
              fontFamily={Fonts.REGULAR}
              color={theme.colors.text}
            />
          </View>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

export default OverallEffect;
