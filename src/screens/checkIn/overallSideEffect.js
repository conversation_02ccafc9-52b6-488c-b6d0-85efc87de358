import { SafeAreaView, View } from 'react-native';
import styles from '../../assets/styles/OverallSideEffect';
import ProfileBuilderButton from '../../components/ProfileBuilderButton';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import CircularSlider from '../../components/CircularSlider';
import { useState, useEffect, useCallback, useContext } from 'react';
import { responsiveStyles } from '../../utils';
import { setOverAllSideEffect } from '../../redux/slices/checkinSlice';
import { useDispatch, useSelector } from 'react-redux';
import { resetLoadingState } from '../../utils';
import { recordEvent } from '../../api/events';
import { ThemeContext } from '../../context/ThemeContext';

const OverallSideEffect = () => {
  const { theme } = useContext(ThemeContext);
  const dispatch = useDispatch();
  const navigation = useNavigation();

  const [screenTime, setScreenTime] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const overAllSideEffect = useSelector(
    (state) => state.checkIn.overAllSideEffect,
  );
  const [selectedIndex, setSelectedIndex] = useState(overAllSideEffect);

  const profileData = useSelector((state) => state.profile);

  resetLoadingState(setIsLoading);

  useEffect(() => {
    setIsLoading(false);
  }, []);

  useEffect(() => {
    dispatch(setOverAllSideEffect(selectedIndex));
  }, [selectedIndex]);

  useFocusEffect(
    useCallback(() => {
      setScreenTime(new Date());
    }, []),
  );

  const saveDosingEvent = async () => {
    const createdAt = new Date().toISOString();
    const attributes = {
      currentScreen: 'overallSideEffect',
      screenTime: (new Date() - screenTime) / 1000,
    };
    await recordEvent(
      'Dosing Event',
      profileData.username,
      true,
      '#D4',
      attributes,
      createdAt,
    );
  };

  const handleNextPress = async () => {
    setIsLoading(true);
    await saveDosingEvent();
    navigation.navigate('LastDose');
  };

  return (
    <SafeAreaView
      style={[styles.safeArea, { backgroundColor: theme.colors.background }]}
    >
      <View
        style={styles.container}
        pointerEvents={isLoading ? 'none' : 'auto'}
      >
        <View style={{ height: 20 }} />
        <CircularSlider
          value={selectedIndex}
          onChange={setSelectedIndex}
          heading="How much does it bother you?"
          width={responsiveStyles.sliderWidth}
        />
      </View>
      <View style={styles.nextButton}>
        <ProfileBuilderButton
          continueText="Next"
          backDisabled={isLoading}
          activityIndicator={isLoading}
          onContinuePress={handleNextPress}
          continueButtonStyle={styles.continueButton}
          continueDisabled={selectedIndex === 0 || isLoading}
        />
      </View>
    </SafeAreaView>
  );
};

export default OverallSideEffect;
