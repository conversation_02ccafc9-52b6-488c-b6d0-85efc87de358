import { useState } from 'react';
import { View } from 'react-native';
import { resetState } from '../../utils';
import styles from '../../assets/styles/WelcomeScreen';
import {
  CommonActions,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import CustomButton from '../../components/CustomButton';
import { ypdWhite } from '../../utils/colors';
import { signOut } from 'aws-amplify/auth';

const WelcomeScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { welcomeScreen = true } = route.params || {};

  const [loading, setLoading] = useState(false);

  const handleStep6 = (e) => {
    e.preventDefault();
    navigation.navigate('Step6', {
      profileBuilding: false,
      marginTop: 50,
    });
  };

  const handleStep7 = (e) => {
    e.preventDefault();
    navigation.navigate('Step7', {
      profileBuilding: false,
      marginTop: 50,
    });
  };

  const handleSignOut = async () => {
    try {
      setLoading(true);
      await resetState(signOut);
      console.log('Logout successful.');
      navigation.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [{ name: 'MainIntro' }],
        }),
      );
    } catch (error) {
      console.error('Error during logout:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.textContainer}>
        <CustomButton
          title="Adjust Cannabis Type"
          variant="teal"
          textStyle={{ color: ypdWhite }}
          onPress={handleStep6}
          style={{ marginTop: 20 }}
          color={ypdWhite}
        />
        <CustomButton
          title="Adjust Dosage Type"
          variant="teal"
          textStyle={{ color: ypdWhite }}
          onPress={handleStep7}
          style={{ marginTop: 20 }}
          color={ypdWhite}
        />
        <CustomButton
          title={'Log out'}
          variant="teal"
          textStyle={{ color: ypdWhite }}
          onPress={handleSignOut}
          style={{ marginTop: 20 }}
          color={ypdWhite}
          activityIndicator={loading}
        />
      </View>
    </View>
  );
};

export default WelcomeScreen;
