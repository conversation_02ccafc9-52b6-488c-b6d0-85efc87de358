import { useCallback, useContext, useState } from 'react';
import { View, Image, SafeAreaView, ScrollView } from 'react-native';
import Heading from '../../components/Heading';
import styles from '../../assets/styles/NotificationScreen';
import CustomButton from '../../components/CustomButton';
import { Fonts, isSmallDevice } from '../../utils';
import {
  CommonActions,
  useFocusEffect,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { setShowNotifications } from '../../redux/slices/profileSlice';
import { updateUserProfile } from '../../api/profile';
import { resetLoadingState } from '../../utils';
import BackButton from '../../components/BackButton';
import { recordEvent } from '../../api/events';
import { ThemeContext } from '../../context/ThemeContext';

const NotificationPermission = () => {
  const { theme } = useContext(ThemeContext);
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const route = useRoute();
  const { profileBuilding = true } = route.params || {};

  const [screenTime, setScreenTime] = useState(null);
  const [isLoadingAllow, setIsLoadingAllow] = useState(false);
  const [isLoadingDisallow, setIsLoadingDisallow] = useState(false);

  useFocusEffect(
    useCallback(() => {
      setScreenTime(new Date());
    }, []),
  );

  const profileData = useSelector((state) => state.profile);

  resetLoadingState(setIsLoadingAllow);
  resetLoadingState(setIsLoadingDisallow);

  const handleNext = async (e, showNotifications) => {
    e.preventDefault();
    if (showNotifications) {
      setIsLoadingAllow(true);
    } else {
      setIsLoadingDisallow(true);
    }
    const updatedProfile = {
      ...profileData,
      showNotifications: showNotifications,
    };
    try {
      await updateUserProfile(updatedProfile);
      dispatch(setShowNotifications(showNotifications));

      const createdAt = new Date().toISOString();
      const attributes = {
        currentScreen: 'notificationPermission',
        screenTime: (new Date() - screenTime) / 1000,
        isCompleted: true,
      };
      await recordEvent(
        'Profile Building Event',
        profileData.username,
        true,
        '#PB10',
        attributes,
        createdAt,
      );
      navigation.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [
            {
              name: 'Tabs',
              params: { screen: 'Home' },
            },
          ],
        }),
      );
    } catch (error) {
      console.error('Error updating profile:', error);
      if (showNotifications) {
        setIsLoadingAllow(false);
      } else {
        setIsLoadingDisallow(false);
      }
    }
  };

  return (
    <SafeAreaView
      style={[styles.mainWrapper, { backgroundColor: theme.colors.background }]}
    >
      <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
        <View style={styles.container}>
          <View>
            <Heading
              text="Allow notifications to follow your progress."
              size="lg"
              fontFamily={Fonts.MEDIUM}
              color={theme.colors.text}
            />
            <View style={styles.imageContainer}>
              <Image
                style={
                  isSmallDevice
                    ? styles.bgImageResponsiveSmall
                    : styles.backgroundImage
                }
                source={require('../../assets/iphoneBackground.png')}
                resizeMode="contain"
              />
            </View>

            <View style={styles.instructionsContiner}>
              <Heading
                text='Tap "Allow" to enable notifications and get timely updates on track with daily check-ins helpful reminders so you never miss a step and milestone alerts to celebrate your achievements.'
                size="sm"
                fontFamily={Fonts.MEDIUM}
                color={theme.colors.text}
              />
            </View>

            <View style={styles.buttonContainer}>
              <CustomButton
                title="Allow Notifications"
                onPress={(e) => handleNext(e, true)}
                disabled={isLoadingAllow || isLoadingDisallow}
                activityIndicator={isLoadingAllow}
                style={
                  isLoadingAllow || isLoadingDisallow
                    ? styles.disabledButton
                    : {}
                }
              />
              <CustomButton
                title="Not Right Now"
                onPress={(e) => handleNext(e, false)}
                variant="grey"
                disabled={isLoadingDisallow || isLoadingAllow}
                activityIndicator={isLoadingDisallow}
                style={
                  isLoadingDisallow || isLoadingAllow
                    ? styles.disabledButton
                    : {}
                }
              />
            </View>
          </View>
          <View>
            {!profileBuilding && (
              <View style={styles.backButton}>
                <BackButton
                  onBackPress={() => navigation.navigate('Profile')}
                  disabled={isLoadingAllow || isLoadingDisallow}
                />
              </View>
            )}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default NotificationPermission;
