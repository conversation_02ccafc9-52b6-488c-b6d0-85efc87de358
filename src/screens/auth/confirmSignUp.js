import {
  Keyboard,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  View,
  ScrollView,
} from 'react-native';
import { useCallback, useState, useContext } from 'react';
import {
  useFocusEffect,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import styles from '../../assets/styles/ConfirmSignUp';
import { parseAWSAuthError, validateInputs } from '../../utils/utils';
import WarningModal from '../profileBuilding/warningModal';
import { confirmSignUp, signIn } from 'aws-amplify/auth';
import { Fonts, useUsername } from '../../utils';
import { ypdRedLight, ypdTextGrey } from '../../utils/colors';
import YpdIcon from '../../components/YpdIcon';
import Heading from '../../components/Heading';
import Input from '../../components/Input';
import CustomButton from '../../components/CustomButton';
import { recordEvent } from '../../api/events';
import { useSelector } from 'react-redux';
import { ThemeContext } from '../../context/ThemeContext';

const ConfirmSignUp = () => {
  const username = useUsername();
  const { theme } = useContext(ThemeContext);
  const navigation = useNavigation();

  const route = useRoute();
  const { password } = route.params || {};

  const [screenTime, setScreenTime] = useState(null);
  const [loading, setLoading] = useState(false);
  const [warningMessage, setWarningMessage] = useState('');
  const [confirmationCode, setConfirmationCode] = useState('');
  const [warningModalVisible, setWarningModalVisible] = useState(false);

  const profileData = useSelector((state) => state.profile);

  const handleConfirm = async () => {
    const error = validateInputs('', false, '', false, confirmationCode, true);
    if (error) {
      setWarningMessage(error);
      setWarningModalVisible(true);
      return;
    }
    try {
      setLoading(true);
      await confirmSignUp({
        username: username,
        confirmationCode: confirmationCode,
      });
      await signIn({ username, password });
      await saveAuthEvent();
      navigation.navigate('Step5');
    } catch (error) {
      console.log(error);

      const message = parseAWSAuthError(error);

      if (message === 'User cannot be confirmed. Current status is CONFIRMED') {
        navigation.navigate('Step5');
      } else {
        setWarningMessage(message);
        setWarningModalVisible(true);
      }
    } finally {
      setLoading(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      setScreenTime(new Date());
    }, []),
  );

  const saveAuthEvent = async () => {
    const createdAt = new Date().toISOString();
    const attributes = {
      currentScreen: 'confirmSignup',
      screenTime: (new Date() - screenTime) / 1000,
    };
    await recordEvent(
      'Auth Event',
      profileData.username,
      true,
      '#A2',
      attributes,
      createdAt,
    );
  };

  return (
    <KeyboardAvoidingView style={{ flex: 1 }}>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
        <ScrollView
          contentContainerStyle={[
            styles.container,
            { backgroundColor: theme.colors.background },
          ]}
          keyboardShouldPersistTaps="handled"
        >
          <YpdIcon />
          <View style={styles.textWrapper}>
            <Heading
              text="Confirm Signup"
              size="lg"
              fontFamily={Fonts.MEDIUM}
              color={theme.colors.text}
            />
            <Heading
              text="We sent the code to your email"
              size="sm"
              color={ypdTextGrey}
            />
          </View>
          <View style={styles.inputContainer}>
            <Input
              placeholder="Confirm code"
              value={confirmationCode}
              onChangeText={setConfirmationCode}
              keyboardType="numeric"
              autoCapitalize="none"
              maxLength={6}
              hasValue={!!confirmationCode}
              disabled={loading}
            />
          </View>
          <View style={styles.buttonContainer}>
            <CustomButton
              title="Confirm"
              onPress={handleConfirm}
              disabled={loading}
              activityIndicator={loading}
            />
            <Heading
              text="Didn't receive a confirmation code yet?"
              size="xsSmall"
              color={theme.colors.linkText}
              fontFamily={Fonts.MEDIUM}
              textAlign="center"
              onPress={() =>
                navigation.navigate('ResendSignUpCode', { password: password })
              }
            />
          </View>
          <WarningModal
            visible={warningModalVisible}
            onClose={() => setWarningModalVisible(false)}
            text={warningMessage}
            icon="alert"
            size={30}
            iconColor="red"
            backgroundColor={ypdRedLight}
          />
        </ScrollView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

export default ConfirmSignUp;
