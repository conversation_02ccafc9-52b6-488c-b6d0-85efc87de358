import { useCallback, useContext, useEffect, useState } from 'react';
import {
  View,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native';
import styles from '../../assets/styles/SignUp';
import {
  fetchUserAttributes,
  getCurrentUser,
  signInWithRedirect,
  signUp,
} from 'aws-amplify/auth';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { parseAWSAuthError, validateInputs } from '../../utils/utils';
import { Fonts, isSmallDevice, useEmail, useName } from '../../utils';
import CustomButton from '../../components/CustomButton';
import {
  setEmailAddress,
  setUserId,
  setUsername,
  updateProfile,
} from '../../redux/slices/profileSlice';
import { useDispatch, useSelector } from 'react-redux';
import { passwordChecker } from '../../utils/utils';
import { ypd<PERSON>reen, ypd<PERSON><PERSON><PERSON><PERSON>, ypdRed } from '../../utils/colors';
import Heading from '../../components/Heading';
import Input from '../../components/Input';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Hub } from '@aws-amplify/core';
import Apple from '../../assets/svgs/profileBuilder/Apple';
import Google from '../../assets/svgs/profileBuilder/Google';
import AuthWarning from '../../components/AuthWarnings';
import {
  createUserProfile,
  getUserProfile,
  updateUserProfile,
} from '../../api/profile';
import { recordEvent } from '../../api/events';
import { ThemeContext } from '../../context/ThemeContext';

const SignUp = () => {
  const name = useName();
  const { theme } = useContext(ThemeContext);
  const dispatch = useDispatch();
  const navigation = useNavigation();

  const [email, setEmail] = useState(useEmail());
  const [password, setPassword] = useState('');
  const [screenTime, setScreenTime] = useState(null);
  const [focusedInput, setFocusedInput] = useState(null);
  const [warningMessage, setWarningMessage] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isAppleLoading, setIsAppleLoading] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [isSignUpLoading, setIsSignUpLoading] = useState(false);

  const profileData = useSelector((state) => state.profile);
  const profileBuildingEvents = useSelector(
    (state) => state.events.profileBuilding,
  );

  useFocusEffect(
    useCallback(() => {
      setScreenTime(new Date());
    }, []),
  );

  useEffect(() => {
    let timeout;

    const unsubscribe = Hub.listen('auth', async ({ payload }) => {
      switch (payload.event) {
        case 'signInWithRedirect':
          await getUser(true);
          break;
        case 'signInWithRedirect_failure':
          setIsGoogleLoading(false);
          setIsAppleLoading(false);
          break;
        case 'customOAuthState':
          console.log(payload.data);
          break;
      }
    });

    return () => {
      clearTimeout(timeout);
      unsubscribe();
    };
  }, []);

  const getUser = async (socialSignup = false) => {
    try {
      const { username, userId } = await getCurrentUser();
      const { email } = await fetchUserAttributes();

      await saveAllProfileBuildingEvents(username);
      await saveAuthEvent(username);

      if (socialSignup) {
        const updatedProfile = {
          ...profileData,
          email: email,
          userId: userId,
          username: username,
          sideEffects: [],
          customSideEffects: [],
        };

        dispatch(setUserId(userId));
        dispatch(setUsername(username));
        dispatch(setEmailAddress(email));

        try {
          await createUserProfile(updatedProfile);
        } catch (e) {
          await updateUserProfile(updatedProfile);
        }
      } else {
        const profileData = await getUserProfile(userId);
        if (profileData) {
          dispatch(updateProfile(profileData));
        }
      }
      navigation.navigate('Step5');
    } catch (error) {
      console.error('Not signed in:', error);
    } finally {
      setIsAppleLoading(false);
      setIsGoogleLoading(false);
      setIsSignUpLoading(false);
    }
  };

  const handleSignUp = async () => {
    if (isSignUpLoading) return;
    setIsSignUpLoading(true);
    const error = validateInputs(email, true, password, true);

    if (error) {
      setWarningMessage(error);
      setIsSignUpLoading(false);
      return;
    }
    try {
      const response = await signUp({
        username: email,
        password: password,
        options: {
          userAttributes: {
            email: email,
          },
        },
      });

      dispatch(setUsername(email));
      dispatch(setEmailAddress(email));
      dispatch(setUserId(response['userId']));

      const updatedProfile = {
        ...profileData,
        email: email,
        username: email,
        userId: response['userId'],
        sideEffects: [],
        customSideEffects: [],
      };
      await createUserProfile(updatedProfile);
      await saveAllProfileBuildingEvents(email);
      await saveAuthEvent(email);
      navigation.navigate('ConfirmSignUp', { password });
    } catch (error) {
      await handleSignupError(error);
    } finally {
      setIsSignUpLoading(false);
    }
  };

  const handleProvidersSignup = async (provider) => {
    if (provider === 'Google' && isGoogleLoading) return;
    if (provider === 'Apple' && isAppleLoading) return;
    if (provider === 'Google') setIsGoogleLoading(true);
    if (provider === 'Apple') setIsAppleLoading(true);

    const timeout = setTimeout(() => {
      if (provider === 'Apple') setIsAppleLoading(false);
      if (provider === 'Google') setIsGoogleLoading(false);
    }, 20000);

    try {
      // Try to work around the search property issue
      const originalLocation = global.location;
      if (global.location && typeof global.location.search === 'string') {
        // Create a mutable location object
        global.location = {
          ...global.location,
          search: global.location.search
        };
      }

      await signInWithRedirect({ provider });

      // Restore original location if we modified it
      if (originalLocation) {
        global.location = originalLocation;
      }

      clearTimeout(timeout);
    } catch (error) {
      console.log(`Error in ${provider} sign-up:`, error);
      clearTimeout(timeout);

      // Handle specific search property errors
      if (error.message && error.message.includes('search')) {
        setWarningMessage(
          `${provider} sign-up is temporarily unavailable. Please use email/password signup or try again later.`
        );
      } else {
        await handleSignupError(error);
      }

      setIsGoogleLoading(false);
      setIsAppleLoading(false);
    }
  };

  const handleSignupError = async (error) => {
    console.log('Error signing up:', error);
    let errorMessage = parseAWSAuthError(error);

    if (errorMessage === 'There is already a signed in user.') {
      await getUser();
      return;
    }

    if (errorMessage === 'PreSignUp failed with error Email already in use.') {
      errorMessage = 'Email already in use.';
    }
    setWarningMessage(errorMessage);
    setIsSignUpLoading(false);
    setIsGoogleLoading(false);
    setIsAppleLoading(false);
  };

  const saveAllProfileBuildingEvents = async (username) => {
    for (const [code, eventData] of Object.entries(profileBuildingEvents)) {
      const { createdAt, attributes } = eventData;
      await recordEvent(
        'Profile Building Event',
        username,
        true,
        code,
        attributes,
        createdAt,
      );
    }
  };

  const saveAuthEvent = async (username) => {
    const createdAt = new Date().toISOString();
    const attributes = {
      currentScreen: 'signup',
      screenTime: (new Date() - screenTime) / 1000,
    };
    await recordEvent(
      'Auth Event',
      username,
      true,
      '#A1',
      attributes,
      createdAt,
    );
  };

  return (
    <>
      {warningMessage && (
        <AuthWarning
          text={warningMessage}
          icon="alert-outline"
          size={30}
          iconColor={ypdRed}
          showCloseButton={true}
          onClose={() => setWarningMessage('')}
        />
      )}
      <SafeAreaView
        style={[styles.safeArea, { backgroundColor: theme.colors.background }]}
      >
        <KeyboardAvoidingView style={{ flex: 1 }}>
          <TouchableWithoutFeedback
            onPress={Keyboard.dismiss}
            accessible={false}
          >
            <View style={styles.container}>
              <Heading
                width={250}
                text={`${name}, let's set up your account!`}
                size="lg"
                fontFamily={Fonts.MEDIUM}
                color={
                  focusedInput === 'email'
                    ? theme.colors.text
                    : theme.colors.textSecondary
                }
                style={isSmallDevice ? styles.headingSmall : styles.heading}
              />

              <View style={styles.inputContainer}>
                <Heading
                  text="Email"
                  fontSize={12}
                  style={styles.inputHeading}
                  color={theme.colors.text}
                />
                <Input
                  placeholder="Enter your email"
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  onFocus={() => setFocusedInput('email')}
                  onBlur={() => setFocusedInput(null)}
                  autoCapitalize="none"
                  autoCorrect={false}
                  accessibilityLabel="Email input"
                  style={[
                    focusedInput === 'email' && { borderColor: ypdGreen },
                  ]}
                  disabled={
                    isGoogleLoading || isAppleLoading || isSignUpLoading
                  }
                />
              </View>

              <View style={styles.inputContainer}>
                <Heading
                  text="Password"
                  fontSize={12}
                  style={styles.inputHeading}
                  color={theme.colors.text}
                />
                <Input
                  placeholder="Enter your password"
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry={!showPassword}
                  onFocus={() => setFocusedInput('password')}
                  onBlur={() => setFocusedInput(null)}
                  accessibilityLabel="Password input"
                  showIcon={true}
                  iconName={showPassword ? 'eye' : 'eye-off'}
                  onIconPress={() => setShowPassword(!showPassword)}
                  disabled={
                    isGoogleLoading || isAppleLoading || isSignUpLoading
                  }
                  style={[{ color: theme.colors.text }]}
                />
                {password.length === 0 || password.length < 8 ? (
                  <Heading
                    text="Password must be at least 8 characters long."
                    fontSize={10}
                    style={[
                      styles.passwordCheck,
                      { color: passwordChecker(focusedInput, password) },
                    ]}
                  />
                ) : null}
              </View>
              <View style={styles.buttonContainer}>
                <CustomButton
                  title="Sign Up"
                  onPress={handleSignUp}
                  variant="green"
                  width="100%"
                  disabled={
                    isGoogleLoading || isAppleLoading || isSignUpLoading
                  }
                  activityIndicator={isSignUpLoading}
                />
              </View>
              <Heading
                text="Already have an account? Login here."
                size="xsSmall"
                color={theme.colors.linkText}
                fontFamily={Fonts.REGULAR}
                textAlign="center"
                onPress={() => navigation.navigate('Login')}
                disabled={isGoogleLoading || isAppleLoading || isSignUpLoading}
              />
              <View style={styles.dividerContainer}>
                <View style={styles.divider} />
                <Heading
                  text="or"
                  size="xsSmall"
                  style={styles.dividerText}
                  color={theme.colors.text}
                />
                <View style={styles.divider} />
              </View>
              <CustomButton
                title="Continue with Google"
                width="100%"
                variant="lightoutline"
                onPress={() => handleProvidersSignup('Google')}
                fontSize={14}
                size={20}
                textColor={theme.colors.text}
                svgIcon={<Google />}
                style={styles.googleButton}
                disabled={isGoogleLoading || isAppleLoading || isSignUpLoading}
                activityIndicator={isGoogleLoading}
              />
              <CustomButton
                title="Continue with Apple"
                width="100%"
                variant="lightoutline"
                onPress={() => handleProvidersSignup('Apple')}
                fontSize={14}
                size={20}
                textColor={theme.colors.text}
                svgIcon={<Apple />}
                disabled={isGoogleLoading || isAppleLoading || isSignUpLoading}
                activityIndicator={isAppleLoading}
              />
            </View>
          </TouchableWithoutFeedback>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </>
  );
};

export default SignUp;
