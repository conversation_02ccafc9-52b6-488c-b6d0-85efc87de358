import {
  SafeAreaView,
  View,
  KeyboardAvoidingView,
  ScrollView,
  Platform,
  Keyboard,
  TouchableWithoutFeedback,
} from 'react-native';
import { useCallback, useContext, useState } from 'react';
import { resetPassword } from 'aws-amplify/auth';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import styles from '../../assets/styles/ResetPassword';
import { Fonts } from '../../utils';
import WarningModal from '../profileBuilding/warningModal';
import { parseAWSAuthError, validateInputs } from '../../utils/utils';
import { ypdRedLight, ypdTextGrey } from '../../utils/colors';
import Heading from '../../components/Heading';
import Input from '../../components/Input';
import CustomButton from '../../components/CustomButton';
import { setEmailAddress } from '../../redux/slices/profileSlice';
import { useDispatch, useSelector } from 'react-redux';
import Reset from '../../assets/svgs/auth/Reset';
import { recordEvent } from '../../api/events';
import { ThemeContext } from '../../context/ThemeContext';

const ResetPassword = () => {
  const { theme } = useContext(ThemeContext);
  const dispatch = useDispatch();
  const navigation = useNavigation();

  const [email, setEmail] = useState('');
  const [screenTime, setScreenTime] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [warningMessage, setWarningMessage] = useState('');
  const [warningModalVisible, setWarningModalVisible] = useState(false);

  const profileData = useSelector((state) => state.profile);

  useFocusEffect(
    useCallback(() => {
      setScreenTime(new Date());
    }, []),
  );

  const saveAuthEvent = async () => {
    const createdAt = new Date().toISOString();
    const attributes = {
      currentScreen: 'resetPassword',
      screenTime: (new Date() - screenTime) / 1000,
    };
    await recordEvent(
      'Auth Event',
      profileData.username,
      true,
      '#A4',
      attributes,
      createdAt,
    );
  };

  const handleResetPassword = async () => {
    const error = validateInputs(email, true);
    if (error) {
      setWarningMessage(error);
      setWarningModalVisible(true);
      return;
    }
    try {
      setIsLoading(true);
      await resetPassword({ username: email });
      dispatch(setEmailAddress(email));
      await saveAuthEvent();
      navigation.navigate('VerifyNewPassword');
    } catch (error) {
      console.log('Error resetting password:', error);
      setWarningMessage(parseAWSAuthError(error));
      setWarningModalVisible(true);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={[styles.wrapper, { backgroundColor: theme.colors.background }]}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView style={styles.wrapper}>
          <ScrollView
            contentContainerStyle={{ flexGrow: 1, justifyContent: 'center' }}
            keyboardShouldPersistTaps="handled"
            style={{ width: '100%' }}
          >
            <View style={styles.container}>
              <Reset style={styles.image} />
              <View style={styles.textContainer}>
                <Heading
                  text={'Reset Password'}
                  fontSize={25}
                  fontFamily={Fonts.MEDIUM}
                  color={theme.colors.text}
                />
                <Heading
                  text="Enter your email to get the code."
                  size="sm"
                  color={ypdTextGrey}
                />
              </View>

              <View style={styles.inputContainer}>
                <Input
                  placeholder="Enter your email"
                  value={email}
                  onChangeText={setEmail}
                  autoCapitalize="none"
                  keyboardType="email-address"
                  disabled={isLoading}
                />
              </View>
              <CustomButton
                title="Reset Password"
                onPress={handleResetPassword}
                disabled={isLoading}
                activityIndicator={isLoading}
              />
              <WarningModal
                visible={warningModalVisible}
                onClose={() => setWarningModalVisible(false)}
                text={warningMessage}
                icon="alert"
                size={30}
                iconColor="red"
                backgroundColor={ypdRedLight}
              />
            </View>
          </ScrollView>
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

export default ResetPassword;
