import {
  SafeAreaView,
  TouchableWithoutFeedback,
  View,
  Keyboard,
} from 'react-native';
import { useCallback, useContext, useState } from 'react';
import { resendSignUpCode } from 'aws-amplify/auth';
import {
  useFocusEffect,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import styles from '../../assets/styles/ResendSignUpCode';
import WarningModal from '../profileBuilding/warningModal';
import CustomButton from '../../components/CustomButton';
import { parseAWSAuthError, validateInputs } from '../../utils/utils';
import { Fonts, useEmail, resetLoadingState } from '../../utils';
import YpdIcon from '../../components/YpdIcon';
import Heading from '../../components/Heading';
import Input from '../../components/Input';
import { recordEvent } from '../../api/events';
import { useSelector } from 'react-redux';
import { ThemeContext } from '../../context/ThemeContext';

const ResendSignUpCode = () => {
  const { theme } = useContext(ThemeContext);
  const navigation = useNavigation();
  const route = useRoute();
  const { password } = route.params || {};

  const [email, setEmail] = useState(useEmail());
  const [screenTime, setScreenTime] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [warningMessage, setWarningMessage] = useState('');
  const [warningModalVisible, setWarningModalVisible] = useState(false);

  resetLoadingState(setIsLoading);

  const profileData = useSelector((state) => state.profile);

  useFocusEffect(
    useCallback(() => {
      setScreenTime(new Date());
    }, []),
  );

  const handleResendCode = async () => {
    const error = validateInputs(email, true);
    if (error) {
      setWarningMessage(error);
      setWarningModalVisible(true);
      return;
    }
    try {
      setIsLoading(true);
      await resendSignUpCode({ username: email });
      console.log('Code resent successfully:', email);
      await saveAuthEvent();
      navigation.navigate('ConfirmSignUp', { password });
    } catch (error) {
      let message = parseAWSAuthError(error);
      if (message === 'User is already confirmed.') {
        message = 'User is already confirmed. Please login.';
      }
      console.log('Error resending code: ', error);
      setWarningMessage(message);
      setWarningModalVisible(true);
    } finally {
      setIsLoading(false);
    }
  };

  const saveAuthEvent = async () => {
    const createdAt = new Date().toISOString();
    const attributes = {
      currentScreen: 'resendSignUpCode',
      screenTime: (new Date() - screenTime) / 1000,
    };
    await recordEvent(
      'Auth Event',
      profileData.username,
      true,
      '#A3',
      attributes,
      createdAt,
    );
  };

  return (
    <TouchableWithoutFeedback onPress={() => Keyboard.dismiss()}>
      <SafeAreaView
        style={[styles.container, { backgroundColor: theme.colors.background }]}
      >
        <YpdIcon />
        <Heading
          text="Resend Confirmation Code"
          fontSize={25}
          fontFamily={Fonts.MEDIUM}
          color={theme.colors.text}
        />
        <View style={styles.inputContainer}>
          <Input
            placeholder="Enter email"
            value={email}
            onChangeText={setEmail}
            autoCapitalize="none"
            keyboardType="email-address"
            disabled={isLoading}
          />
        </View>
        <CustomButton
          title="Resend Code"
          alignSelf="center"
          onPress={handleResendCode}
          disabled={isLoading}
          activityIndicator={isLoading}
        />
        <Heading
          text="Already have an account? Login here."
          size="xsSmall"
          color={theme.colors.linkText}
          fontFamily={Fonts.REGULAR}
          textAlign="center"
          onPress={() => navigation.navigate('Login')}
        />
        <WarningModal
          visible={warningModalVisible}
          onClose={() => setWarningModalVisible(false)}
          text={warningMessage}
          icon="alert"
          size={30}
          iconColor="red"
        />
      </SafeAreaView>
    </TouchableWithoutFeedback>
  );
};

export default ResendSignUpCode;
