import {
  View,
  KeyboardAvoidingView,
  ScrollView,
  Platform,
  Keyboard,
  TouchableWithoutFeedback,
} from 'react-native';
import { useCallback, useContext, useState } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { confirmResetPassword } from 'aws-amplify/auth';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import styles from '../../assets/styles/VerifyNewPassword';
import WarningModal from '../profileBuilding/warningModal';
import { Fonts, isSmallDevice, useEmail } from '../../utils';
import { parseAWSAuthError, validateInputs } from '../../utils/utils';
import { passwordChecker } from '../../utils/utils';
import { ypdTextGrey } from '../../utils/colors';
import Input from '../../components/Input';
import Heading from '../../components/Heading';
import CustomButton from '../../components/CustomButton';
import { recordEvent } from '../../api/events';
import { useSelector } from 'react-redux';
import { ThemeContext } from '../../context/ThemeContext';

const VerifyNewPassword = () => {
  const username = useEmail();
  const { theme } = useContext(ThemeContext);
  const navigation = useNavigation();

  const [screenTime, setScreenTime] = useState(null);
  const [focusedInput, setFocusedInput] = useState(null);
  const [newPassword, setNewPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [warningMessage, setWarningMessage] = useState('');
  const [confirmationCode, setConfirmationCode] = useState('');
  const [warningModalVisible, setWarningModalVisible] = useState(false);

  const profileData = useSelector((state) => state.profile);

  useFocusEffect(
    useCallback(() => {
      setScreenTime(new Date());
    }, []),
  );

  const saveAuthEvent = async () => {
    const createdAt = new Date().toISOString();
    const attributes = {
      currentScreen: 'verifyNewPassword',
      screenTime: (new Date() - screenTime) / 1000,
    };
    await recordEvent(
      'Auth Event',
      profileData.username,
      true,
      '#A5',
      attributes,
      createdAt,
    );
  };

  const handleSubmit = async () => {
    const error = validateInputs(
      '',
      false,
      newPassword,
      true,
      confirmationCode,
      true,
    );
    if (error) {
      setWarningMessage(error);
      setWarningModalVisible(true);
      return;
    }
    try {
      setIsLoading(true);
      await confirmResetPassword({ username, confirmationCode, newPassword });
      await saveAuthEvent();
      navigation.navigate('Login');
    } catch (error) {
      console.log('Error confirming reset password:', error);
      setWarningMessage(parseAWSAuthError(error));
      setWarningModalVisible(true);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1 }}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <ScrollView
          contentContainerStyle={{ flexGrow: 1, justifyContent: 'center' }}
          keyboardShouldPersistTaps="handled"
        >
          <SafeAreaView
            style={[
              styles.container,
              { backgroundColor: theme.colors.background },
            ]}
          >
            <Heading
              text="Reset Your Password:"
              size="lg"
              fontFamily={Fonts.MEDIUM}
              style={isSmallDevice ? styles.headingSmall : styles.heading}
            />

            <View style={styles.wrapper}>
              <View style={styles.inputContainer}>
                <View style={styles.textWrapper}>
                  <Heading
                    text="Confirmation code"
                    fontSize={12}
                    style={styles.inputHeading}
                  />
                </View>
                <Input
                  placeholder="Confirmation code"
                  value={confirmationCode}
                  onChangeText={setConfirmationCode}
                  onFocus={() => setFocusedInput('confirmationCode')}
                  onBlur={() => setFocusedInput(null)}
                  keyboardType="numeric"
                  disabled={isLoading}
                />

                <View style={styles.inputContainer}>
                  <Heading
                    text="Password"
                    fontSize={12}
                    style={styles.inputHeading}
                  />
                  <Input
                    placeholder="New password"
                    value={newPassword}
                    onChangeText={setNewPassword}
                    onFocus={() => setFocusedInput('newPassword')}
                    onBlur={() => setFocusedInput(null)}
                    keyboardType="default"
                    secureTextEntry={!showPassword}
                    showIcon={true}
                    iconName={showPassword ? 'eye' : 'eye-off'}
                    onIconPress={() => setShowPassword(!showPassword)}
                    disabled={isLoading}
                  />
                  {newPassword.length === 0 || newPassword.length < 8 ? (
                    <Heading
                      text="Password must be at least 8 characters long."
                      style={[
                        styles.forgotPassword,
                        {
                          color: passwordChecker(focusedInput, newPassword),
                        },
                      ]}
                    />
                  ) : null}
                </View>
              </View>
              <CustomButton
                title="Create Password"
                onPress={handleSubmit}
                width="100%"
                disabled={isLoading}
                activityIndicator={isLoading}
              />
            </View>
            <WarningModal
              visible={warningModalVisible}
              onClose={() => setWarningModalVisible(false)}
              text={warningMessage}
              icon="alert"
              size={30}
              iconColor="red"
            />
          </SafeAreaView>
        </ScrollView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

export default VerifyNewPassword;
