import { useCallback, useContext, useEffect, useState } from 'react';
import {
  View,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Keyboard,
  SafeAreaView,
} from 'react-native';
import styles from '../../assets/styles/SignUp';
import {
  signInWithRedirect,
  signIn,
  signOut,
  getCurrentUser,
} from 'aws-amplify/auth';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { parseAWSAuthError, validateInputs } from '../../utils/utils';
import { Fonts, isSmallDevice, useEmail, useName } from '../../utils';
import { ypdRed } from '../../utils/colors';
import CustomButton from '../../components/CustomButton';
import Heading from '../../components/Heading';
import Input from '../../components/Input';
import {
  setProfileNotCreated,
  setUsername,
  updateProfile,
} from '../../redux/slices/profileSlice';
import { useDispatch } from 'react-redux';
import { Hub } from '@aws-amplify/core';
import Apple from '../../assets/svgs/profileBuilder/Apple';
import Google from '../../assets/svgs/profileBuilder/Google';
import AuthWarning from '../../components/AuthWarnings';
import { getUserProfile } from '../../api/profile';
import { recordEvent } from '../../api/events';
import { ThemeContext } from '../../context/ThemeContext';

const Login = () => {
  const name = useName();
  const { theme } = useContext(ThemeContext);
  const dispatch = useDispatch();
  const navigation = useNavigation();

  const [email, setEmail] = useState(useEmail());
  const [screenTime, setScreenTime] = useState(null);
  const [password, setPassword] = useState('');
  const [focusedInput, setFocusedInput] = useState(null);
  const [warningMessage, setWarningMessage] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoginSubmitting, setIsLoginSubmitting] = useState(false);
  const [isAppleSubmitting, setIsAppleSubmitting] = useState(false);
  const [isGoogleSubmitting, setIsGoogleSubmitting] = useState(false);

  const isAnySubmitting =
    isLoginSubmitting || isGoogleSubmitting || isAppleSubmitting;

  useFocusEffect(
    useCallback(() => {
      setScreenTime(new Date());
    }, []),
  );

  const saveAuthEvent = async (username) => {
    const createdAt = new Date().toISOString();
    const attributes = {
      currentScreen: 'login',
      screenTime: (new Date() - screenTime) / 1000,
    };
    await recordEvent(
      'Auth Event',
      username,
      true,
      '#A6',
      attributes,
      createdAt,
    );
  };

  useEffect(() => {
    let timeout;

    const unsubscribe = Hub.listen('auth', ({ payload }) => {
      console.log('Auth Hub event:', payload.event);
      try {
        switch (payload.event) {
          case 'signInWithRedirect':
            console.log('Social sign-in successful, calling getUser()');
            getUser().catch(error => {
              console.log('Error in getUser after social signin:', error);
              handleLoginError(error);
            });
            break;
          case 'signInWithRedirect_failure':
            console.log('Social sign-in failed:', payload);
            setWarningMessage('Social sign-in failed. Please try again.');
            setIsGoogleSubmitting(false);
            setIsAppleSubmitting(false);
            break;
          case 'customOAuthState':
            console.log('Custom OAuth state:', payload.data);
            break;
        }
      } catch (hubError) {
        console.log('Error in Hub listener:', hubError);
        setIsGoogleSubmitting(false);
        setIsAppleSubmitting(false);
      }
    });

    return () => {
      clearTimeout(timeout);
      unsubscribe();
    };
  }, []);

  const checkProfileCompleteness = (profileData) => {
    const requiredFields = ['name', 'age', 'gender'];
    const missingFields = requiredFields.filter(
      (field) => !profileData[field] || profileData[field] === '',
    );
    console.log('Profile completeness check details:', {
      name: profileData.name,
      age: profileData.age,
      gender: profileData.gender,
      missingFields,
      isComplete: missingFields.length === 0
    });
    return {
      isComplete: missingFields.length === 0,
      missingFields,
    };
  };

  const getUser = async () => {
    try {
      const { userId } = await getCurrentUser();
      console.log('Getting user profile for userId:', userId);
      const profileData = await getUserProfile(userId);
      console.log('Profile data retrieved:', profileData);

      if (profileData) {
        const { isComplete, missingFields } = checkProfileCompleteness(profileData);
        console.log('Profile completeness check:', { isComplete, missingFields });

        if (isComplete) {
          dispatch(updateProfile(profileData));
          // Use username or email or userId for auth event
          const userIdentifier = profileData.username || profileData.email || userId;
          await saveAuthEvent(userIdentifier);

          // Add small delay to prevent navigation race conditions
          setTimeout(() => {
            try {
              navigation.reset({
                index: 0,
                routes: [
                  {
                    name: 'Tabs',
                    state: {
                      routes: [{ name: 'HomeScreen' }],
                    },
                  },
                ],
              });
            } catch (navError) {
              console.log('Navigation error, trying alternative:', navError);
              try {
                navigation.navigate('Tabs', { screen: 'HomeScreen' });
              } catch (fallbackError) {
                console.log('Fallback navigation failed:', fallbackError);
                navigation.navigate('HomeScreen');
              }
            }
          }, 100);
        } else {
          // Profile exists but is incomplete - this applies to both regular and social signups
          console.log('Profile incomplete, navigating to completion flow');
          dispatch(updateProfile(profileData));
          dispatch(setProfileNotCreated(true));
          setWarningMessage(
            'Profile not created yet. please create your profile first.',
          );
          setTimeout(() => {
            setWarningMessage('');
            console.log('Navigating to ManageAccount for profile completion');
            try {
              navigation.reset({
                index: 0,
                routes: [
                  {
                    name: 'ManageAccount',
                    params: { isProfileCompletion: true },
                  },
                ],
              });
            } catch (navError) {
              console.log('Navigation error, trying alternative:', navError);
              navigation.navigate('ManageAccount', { isProfileCompletion: true });
            }
          }, 3000);
        }
      } else {
        setWarningMessage('Profile not created yet. Logging out...');
        setTimeout(async () => {
          setWarningMessage('');
          await signOut({ global: true });
          navigation.navigate('MainIntro');
        }, 3000);
      }
    } catch (error) {
      console.log('Error in getUser function:', error);
      // Handle specific navigation errors
      if (error.message && error.message.includes('search')) {
        console.log('Navigation search property error detected, using fallback navigation');
        try {
          navigation.navigate('MainIntro');
        } catch (navError) {
          console.log('Fallback navigation also failed:', navError);
          setWarningMessage('Navigation error occurred. Please restart the app.');
        }
      } else {
        await handleLoginError(error);
      }
    } finally {
      setIsGoogleSubmitting(false);
      setIsAppleSubmitting(false);
    }
  };

  const handleLogin = async () => {
    if (isAnySubmitting) return;
    const error = validateInputs(email, true, password, true);
    if (error) {
      setWarningMessage(error);
      return;
    }
    setIsLoginSubmitting(true);
    try {
      const response = await signIn({ username: email, password });

      if (response['nextStep']['signInStep'] === 'CONFIRM_SIGN_UP') {
        dispatch(setUsername(email));
        await saveAuthEvent(email);
        navigation.navigate('ConfirmSignUp', { password });
      } else {
        await getUser();
      }
    } catch (error) {
      await handleLoginError(error);
    }
  };

  const handleProvidersSignup = async (provider) => {
    if (isAnySubmitting) return;
    if (provider === 'Apple') setIsAppleSubmitting(true);
    if (provider === 'Google') setIsGoogleSubmitting(true);

    const timeout = setTimeout(() => {
      console.log(`${provider} sign-in timeout reached`);
      if (provider === 'Apple') setIsAppleSubmitting(false);
      if (provider === 'Google') setIsGoogleSubmitting(false);
      setWarningMessage('Sign-in timed out. Please try again.');
    }, 20000);

    try {
      console.log(`Starting ${provider} sign-in`);
      await signInWithRedirect({ provider });
      clearTimeout(timeout);
    } catch (error) {
      console.log(`Error in ${provider} sign-in:`, error);
      clearTimeout(timeout);
      // Handle specific search property errors
      if (error.message && error.message.includes('search')) {
        setWarningMessage('Authentication error occurred. Please try again.');
        setIsAppleSubmitting(false);
        setIsGoogleSubmitting(false);
      } else {
        await handleLoginError(error);
      }
    }
  };

  const handleLoginError = async (error) => {
    console.log('Error during login:', error);

    try {
      let errorMessage = parseAWSAuthError(error);

      if (errorMessage === 'There is already a signed in user.') {
        await getUser();
        return;
      }

      if (errorMessage === 'PreSignUp failed with error Email already in use.') {
        errorMessage = 'Email already in use.';
      }
      setWarningMessage(errorMessage);
    } catch (parseError) {
      console.log('Error parsing AWS error:', parseError);
      setWarningMessage('An error occurred during login. Please try again.');
    }

    setIsLoginSubmitting(false);
    setIsGoogleSubmitting(false);
    setIsAppleSubmitting(false);
  };

  return (
    <>
      {warningMessage && (
        <AuthWarning
          text={warningMessage}
          icon="alert-outline"
          size={30}
          iconColor={ypdRed}
          showCloseButton={true}
          onClose={() => setWarningMessage('')}
        />
      )}
      <SafeAreaView
        style={{ flex: 1, backgroundColor: theme.colors.background }}
      >
        <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding">
          <TouchableWithoutFeedback
            onPress={Keyboard.dismiss}
            accessible={false}
          >
            <View style={styles.container}>
              <Heading
                text={name ? `${name}, welcome back!` : 'Welcome back!'}
                size="lg"
                fontFamily={Fonts.MEDIUM}
                color={
                  focusedInput === 'email'
                    ? theme.colors.text
                    : theme.colors.textSecondary
                }
                style={isSmallDevice ? styles.headingSmall : styles.heading}
              />
              <View style={styles.wrapper}>
                <View style={styles.inputContainer}>
                  <Heading
                    text="Email"
                    fontSize={12}
                    style={styles.inputHeading}
                    color={theme.colors.text}
                  />
                  <Input
                    placeholder="Enter your email"
                    value={email}
                    onChangeText={setEmail}
                    keyboardType="email-address"
                    onFocus={() => setFocusedInput('email')}
                    onBlur={() => setFocusedInput(null)}
                    autoCapitalize="none"
                    autoCorrect={false}
                    accessibilityLabel="Email input"
                    disabled={isAnySubmitting}
                  />
                </View>

                <View style={styles.inputContainer}>
                  <Heading
                    text="Password"
                    fontSize={12}
                    style={styles.inputHeading}
                    color={theme.colors.text}
                  />
                  <Input
                    placeholder="Enter your password"
                    value={password}
                    onChangeText={setPassword}
                    secureTextEntry={!showPassword}
                    onFocus={() => setFocusedInput('password')}
                    onBlur={() => setFocusedInput(null)}
                    accessibilityLabel="Password input"
                    showIcon={true}
                    iconName={showPassword ? 'eye' : 'eye-off'}
                    onIconPress={() => setShowPassword(!showPassword)}
                    disabled={isAnySubmitting}
                    style={[{ color: theme.colors.text }]}
                  />
                  <Heading
                    text="I forgot my password"
                    size="link"
                    style={[
                      styles.forgotPassword,
                      { color: theme.colors.linkText },
                    ]}
                    onPress={() => navigation.navigate('ResetPassword')}
                  />
                </View>
                <View style={styles.buttonContainer}>
                  <CustomButton
                    title="Log in"
                    onPress={handleLogin}
                    variant="green"
                    width="100%"
                    disabled={isAnySubmitting}
                    activityIndicator={isLoginSubmitting}
                  />
                </View>
                <View style={styles.dividerContainer}>
                  <View style={styles.divider} />
                  <Heading
                    text="or"
                    size="xsSmall"
                    style={styles.dividerText}
                    color={theme.colors.text}
                  />
                  <View style={styles.divider} />
                </View>
                <CustomButton
                  title="Continue with Google"
                  width="100%"
                  variant="lightoutline"
                  onPress={() => handleProvidersSignup('Google')}
                  fontSize={14}
                  size={20}
                  textColor={theme.colors.text}
                  svgIcon={<Google />}
                  style={[styles.googleButton, { color: theme.colors.text }]}
                  disabled={isAnySubmitting}
                  activityIndicator={isGoogleSubmitting}
                />
                <CustomButton
                  title="Continue with Apple"
                  width="100%"
                  variant="lightoutline"
                  onPress={() => handleProvidersSignup('Apple')}
                  fontSize={14}
                  size={20}
                  textColor={theme.colors.text}
                  svgIcon={<Apple />}
                  disabled={isAnySubmitting}
                  activityIndicator={isAppleSubmitting}
                />
              </View>
            </View>
          </TouchableWithoutFeedback>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </>
  );
};

export default Login;
