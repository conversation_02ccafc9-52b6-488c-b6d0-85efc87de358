import { useState, useRef, useEffect, useContext } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  PanResponder,
} from 'react-native';
import styles from '../../assets/styles/MainIntroStyles';
import { useNavigation } from '@react-navigation/native';
import CustomButton from '../../components/CustomButton';
import Heading from '../../components/Heading';
import { Fonts, responsiveStyles } from '../../utils';
import { ypdOldGreen } from '../../utils/colors';
import Confirm from '../../assets/svgs/profileBuilder/Confirm';
import Option from '../../assets/svgs/profileBuilder/Option';
import Web from '../../assets/svgs/profileBuilder/Web';
import { ThemeContext } from '../../context/ThemeContext';

const MainIntro = () => {
  const { theme } = useContext(ThemeContext);
  const navigation = useNavigation();

  const [slideIndex, setSlideIndex] = useState(0);
  const [swipedPastLastSlide, setSwipedPastLastSlide] = useState(false);

  useEffect(() => {
    if (swipedPastLastSlide) {
      navigation.navigate('Step1');
      setSwipedPastLastSlide(false);
    }
  }, [swipedPastLastSlide]);

  const slides = [
    {
      image: <Web />,
      description:
        'Cut through the confusion with science-backed insights. Choose the right products and dosages with confidence.',
      formattedTitle: (
        <>
          <Heading
            text="Clarity in a cloud of confusion:"
            size="lg"
            textAlign="center"
            width={315}
            fontFamily={Fonts.MEDIUM}
            color={theme.colors.text}
          />
          <Heading
            text="CBD & THC explained"
            size="lg"
            fontFamily={Fonts.MEDIUM}
            color={ypdOldGreen}
          />
        </>
      ),
    },
    {
      image: <Option />,
      description:
        'Your suggestions are grounded in clinician-based and scientific research, personalized to you and your current goals.',
      formattedTitle: (
        <>
          <Heading
            text="A scientific approach to relief,"
            size="lg"
            textAlign="center"
            width={290}
            fontFamily={Fonts.MEDIUM}
            color={theme.colors.text}
          />
          <Heading
            text="not a high."
            size="lg"
            fontFamily={Fonts.MEDIUM}
            color={ypdOldGreen}
          />
        </>
      ),
    },
    {
      image: <Confirm />,
      description:
        'Start your journey to better relief! Track your progress, set health goals, and discover what works best for you.',
      formattedTitle: (
        <>
          <Heading
            text="Let’s get started! Find your"
            size="lg"
            textAlign="center"
            width={290}
            fontFamily={Fonts.MEDIUM}
            color={theme.colors.text}
          />
          <Heading
            text="perfect dose."
            size="lg"
            fontFamily={Fonts.MEDIUM}
            color={ypdOldGreen}
          />
        </>
      ),
    },
  ];

  const handleNext = () => {
    setSlideIndex((prev) => (prev + 1) % slides.length);

    if (slideIndex === 2) {
      navigation.navigate('Step1');
    }
  };

  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (_, gestureState) =>
        Math.abs(gestureState.dx) > 10,
      onPanResponderRelease: (_, { dx }) => {
        if (Math.abs(dx) > 50) {
          setSlideIndex((prev) => {
            if (dx > 0) {
              return prev > 0 ? prev - 1 : prev;
            } else {
              if (prev < slides.length - 1) {
                return prev + 1;
              } else {
                setSwipedPastLastSlide(true);
                return prev;
              }
            }
          });
        }
      },
    }),
  ).current;

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
    >
      <View style={styles.contentWrapper} {...panResponder.panHandlers}>
        <View style={styles.textContainer}>
          {slides[slideIndex].formattedTitle}
        </View>
        <View style={styles.imageContainer}>{slides[slideIndex].image}</View>
        <Heading
          text={slides[slideIndex].description}
          size="sm"
          width={315}
          fontFamily={Fonts.REGULAR}
          textAlign={'center'}
          fontSize={responsiveStyles.fontSize}
          style={{ alignSelf: 'center' }}
          color={theme.colors.text}
        />
      </View>
      <View style={styles.bottomContainer}>
        <View style={styles.bottomContainer}>
          <View style={styles.dotContainer}>
            {slides.map((_, index) => (
              <View
                key={index}
                style={[
                  styles.dot,
                  slideIndex === index
                    ? { backgroundColor: theme.colors.text }
                    : { backgroundColor: theme.colors.sliderDotColor },
                ]}
              />
            ))}
          </View>
          <CustomButton
            title="Continue"
            onPress={handleNext}
            variant="outline"
          />
        </View>
        <View style={styles.bottomLoginButton}>
          <TouchableOpacity onPress={() => navigation.navigate('Login')}>
            <Heading
              text={
                <>
                  Already a member?{' '}
                  <Text style={styles.logInHighlight}>Log in</Text>
                </>
              }
              size="xsmall"
              color={theme.colors.text}
            />
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default MainIntro;
