import { useState, useEffect, useContext } from 'react';
import { View, Modal, Dimensions } from 'react-native';
import { IconButton } from 'react-native-paper';
import Heading from '../../components/Heading';
import { Fonts } from '../../utils';
import styles from '../../assets/styles/WarningModal';
import { ThemeContext } from '../../context/ThemeContext';

const WarningModal = ({
  visible,
  onClose,
  text,
  icon,
  size,
  iconColor,
  showCloseButton = true,
}) => {
  const { theme } = useContext(ThemeContext);

  const [windowWidth, setWindowWidth] = useState(
    Dimensions.get('window').width,
  );

  useEffect(() => {
    const updateDimensions = () => {
      setWindowWidth(Dimensions.get('window').width);
    };
    const dimensionsListener = Dimensions.addEventListener(
      'change',
      updateDimensions,
    );
    return () => {
      dimensionsListener.remove();
    };
  }, []);

  const modalWidth = windowWidth < 600 ? windowWidth * 1 : 500;

  return (
    <View>
      <Modal
        visible={visible}
        transparent
        animationType="fade"
        onRequestClose={onClose}
      >
        <View style={styles.overlay}>
          <View
            style={[
              styles.contentContainer,
              {
                backgroundColor: theme.colors.backgroundRed,
                width: modalWidth,
                maxWidth: windowWidth,
              },
            ]}
          >
            <View style={styles.wrapper}>
              <IconButton
                icon={icon}
                size={size}
                iconColor={iconColor}
                style={styles.icon}
              />
              <View style={styles.textContainer}>
                <Heading
                  text={text}
                  size="sm"
                  fontFamily={Fonts.REGULAR}
                  color={theme.colors.text}
                />
              </View>
              {showCloseButton && (
                <IconButton
                  icon="close"
                  style={styles.closeButton}
                  size={24}
                  iconColor={theme.colors.text}
                  onPress={onClose}
                />
              )}
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default WarningModal;
