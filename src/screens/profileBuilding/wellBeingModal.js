import { useEffect, useRef } from 'react';
import { Modal, View, Animated, BackHandler, Dimensions } from 'react-native';
import styles from '../../assets/styles/WellBiengModal';
import { ypdOldGreen, ypdWhite } from '../../utils/colors';
import Alert from '../../assets/svgs/mainIntro/Alert';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

const WellBeingModal = ({ visible }) => {
  const growth = useRef(new Animated.Value(0)).current;
  const backgroundColor = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (visible) {
      growth.setValue(0);
      backgroundColor.setValue(0);

      Animated.parallel([
        Animated.timing(growth, {
          toValue: 1,
          duration: 5000,
          useNativeDriver: true,
        }),
        Animated.timing(backgroundColor, {
          toValue: 1,
          duration: 5000,
          useNativeDriver: false,
        }),
      ]).start(() => {
        BackHandler.exitApp();
      });
    }
  }, [visible]);

  const animatedBackgroundColor = backgroundColor.interpolate({
    inputRange: [0, 0.2, 1],
    outputRange: [{ ypdWhite }, { ypdWhite }, { ypdOldGreen }],
  });

  return (
    <Modal visible={visible} animationType="slide">
      <Animated.View
        style={[
          styles.mainContainer,
          { backgroundColor: animatedBackgroundColor },
        ]}
      >
        <View style={styles.modalContent}>
          <View style={styles.centeredContent}>
            <Animated.View
              style={[
                styles.modalIconContainer,
                {
                  transform: [
                    {
                      scale: growth.interpolate({
                        inputRange: [0, 0.2, 1],
                        outputRange: [
                          1,
                          1,
                          Math.max(SCREEN_WIDTH / 127, SCREEN_HEIGHT / 113) * 3,
                        ],
                      }),
                    },
                  ],
                },
              ]}
            >
              <Alert
                style={[
                  styles.modalIcon,
                  {
                    opacity: growth.interpolate({
                      inputRange: [0, 0.2],
                      outputRange: [1, 0],
                      extrapolate: 'clamp',
                    }),
                  },
                ]}
              />
              <Animated.View
                style={[
                  styles.triangle,
                  {
                    opacity: growth.interpolate({
                      inputRange: [0, 0.2, 1],
                      outputRange: [0, 1, 1],
                    }),
                  },
                ]}
              />
            </Animated.View>

            <Animated.Text
              style={[
                styles.modalTitle,
                {
                  opacity: growth.interpolate({
                    inputRange: [0, 0.2, 1],
                    outputRange: [1, 1, 0],
                  }),
                },
              ]}
            >
              Your well-being is our top priority. To ensure safety, we're
              unable to offer our services.
            </Animated.Text>

            <Animated.Text
              style={[
                styles.modalSubtext,
                {
                  opacity: growth.interpolate({
                    inputRange: [0, 0.2, 1],
                    outputRange: [1, 1, 0],
                  }),
                },
              ]}
            >
              Stay safe and healthy!
            </Animated.Text>
          </View>
        </View>
      </Animated.View>
    </Modal>
  );
};

export default WellBeingModal;
