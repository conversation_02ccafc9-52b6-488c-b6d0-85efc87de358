import { useContext, useState } from 'react';
import {
  View,
  Modal,
  TouchableWithoutFeedback,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import Heading from '../../components/Heading';
import { Fonts } from '../../utils';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import styles from '../../assets/styles/CannabisDropdown';
import {
  ypdBlack,
  ypdDarkGrey,
  ypdTextGrey,
  ypdWhite,
} from '../../utils/colors';
import { ThemeContext } from '../../context/ThemeContext';

const CannabisDropDown = ({
  data,
  label,
  placeholder,
  value,
  disabled,
  onValueChange,
}) => {
  const { theme } = useContext(ThemeContext);
  const [isFocus, setIsFocus] = useState(false);

  return (
    <View style={styles.container}>
      {label && (
        <Heading
          text={label}
          size="xssmall"
          fontFamily={Fonts.MEDIUM}
          color={theme.colors.text}
          style={[styles.heading, disabled && { opacity: 0.5 }]}
        />
      )}
      <TouchableOpacity
        style={[
          styles.dropdown,
          {
            borderColor: value
              ? disabled
                ? ypdDarkGrey
                : theme.dark
                  ? ypdWhite
                  : ypdBlack
              : ypdDarkGrey,
          },
          disabled && { backgroundColor: theme.colors.lightTextGray },
        ]}
        disabled={disabled}
        onPress={() => setIsFocus(true)}
      >
        <Heading
          text={
            value
              ? data.find((item) => item.value === value)?.label
              : placeholder
          }
          style={[
            styles.placeholderStyle,
            { color: value ? theme.colors.text : ypdTextGrey },
          ]}
        />
        <MaterialIcons
          name="arrow-drop-down"
          size={24}
          color={theme.colors.text}
        />
      </TouchableOpacity>

      <Modal
        transparent
        animationType="fade"
        visible={isFocus}
        onRequestClose={() => setIsFocus(false)}
      >
        <TouchableWithoutFeedback onPress={() => setIsFocus(false)}>
          <View style={styles.modalOverlay}>
            <View
              style={[
                styles.modalContent,
                {
                  backgroundColor: theme.colors.background,
                },
              ]}
            >
              <MaterialIcons
                name="close"
                size={24}
                style={styles.closeButton}
                color={theme.colors.text}
                onPress={() => setIsFocus(false)}
              />

              {label && (
                <Heading
                  text={label}
                  size="sm"
                  fontFamily={Fonts.BOLD}
                  textAlign="center"
                  color={theme.colors.text}
                />
              )}
              <FlatList
                data={data}
                keyExtractor={(item) => item.value.toString()}
                style={styles.dropdownList}
                renderItem={({ item }) => (
                  <Heading
                    text={item.label}
                    style={styles.dropdownItem}
                    size="sm"
                    fontFamily={Fonts.REGULAR}
                    color={theme.colors.text}
                    onPress={() => {
                      onValueChange(item.value);
                      setIsFocus(false);
                    }}
                  />
                )}
                ItemSeparatorComponent={() => <View style={styles.separator} />}
              />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </View>
  );
};

export default CannabisDropDown;
