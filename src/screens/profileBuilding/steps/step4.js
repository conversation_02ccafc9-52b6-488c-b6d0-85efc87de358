import { useState, useEffect, useMemo, useCallback, useContext } from 'react';
import {
  View,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Keyboard,
  InteractionManager,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  BackHandler,
} from 'react-native';
import Heading from '../../../components/Heading';
import Input from '../../../components/Input';
import styles from '../../../assets/styles/ProfileBuilderStyles';
import { IconButton } from 'react-native-paper';
import { Fonts, healthOptions } from '../../../utils';
import { useSelector, useDispatch } from 'react-redux';
import {
  setCustomProblems,
  setPrivacyTermsAccepted,
  setProblems,
} from '../../../redux/slices/profileSlice';
import WarningModal from '../warningModal';
import TermsModal from '../termsModal';
import ProgressBar from '../progressBar';
import {
  useFocusEffect,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ypdOldGreen, ypdRedLight, ypdWhite } from '../../../utils/colors';
import ProfileBuilderButton from '../../../components/ProfileBuilderButton';
import { resetLoadingState } from '../../../utils';
import { updateUserProfile } from '../../../api/profile';
import { addProfileBuildingEvent } from '../../../redux/slices/eventsSlice';
import { recordEvent } from '../../../api/events';
import { ThemeContext } from '../../../context/ThemeContext';

const Step4 = () => {
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const route = useRoute();
  const { profileBuilding = true } = route.params || {};

  const PROBLEM_SELECTION_LIMIT = process.env.PROBLEM_SELECTION_LIMIT || 3;
  const { theme } = useContext(ThemeContext);

  const [screenTime, setScreenTime] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [customProblem, setCustomProblem] = useState('');
  const [termsAgreed, setTermsAgreed] = useState(false);
  const [problemsLimit, setProblemsLimit] = useState(false);
  const [privacyTermsModal, setShowTermsModal] = useState(false);
  const [healthConcernsOptions, setHealthConcernsOptions] = useState([]);

  resetLoadingState(setIsLoading);
  const profileData = useSelector((state) => state.profile);

  const [oldProblems] = useState(profileData?.problems);
  const [oldCustomProblems] = useState(profileData?.customProblems);

  useFocusEffect(
    useCallback(() => {
      setScreenTime(new Date());
    }, []),
  );

  useEffect(() => {
    const backAction = () => {
      if (profileData?.problems.length === 0 || isLoading) {
        return true;
      }
      handleBackPress();
      return true;
    };

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );

    return () => backHandler.remove();
  }, [profileData?.problems.length, isLoading]);

  useEffect(() => {
    const options = healthOptions.filter(
      (option) => !profileData?.problems.includes(option),
    );
    setHealthConcernsOptions(options);
  }, [profileData?.problems]);

  const handleProblemsChange = (concern) => {
    const updatedProblems = profileData?.problems.includes(concern)
      ? profileData?.problems.filter((item) => item !== concern)
      : [...profileData?.problems, concern];

    if (updatedProblems.length <= PROBLEM_SELECTION_LIMIT) {
      dispatch(setProblems(updatedProblems));

      if (!healthOptions.includes(concern)) {
        const updatedCustomProblems = profileData?.customProblems.includes(
          concern,
        )
          ? profileData?.customProblems.filter((item) => item !== concern)
          : [...profileData?.customProblems, concern];
        dispatch(setCustomProblems(updatedCustomProblems));
      }
    } else {
      setProblemsLimit(true);
    }
  };

  const handleCustomProblem = () => {
    const trimmedProblem = customProblem.trim();

    if (
      trimmedProblem &&
      !profileData?.problems.includes(trimmedProblem) &&
      !profileData?.customProblems.includes(trimmedProblem)
    ) {
      if (profileData?.problems.length < PROBLEM_SELECTION_LIMIT) {
        const updatedProblems = [...profileData?.problems, trimmedProblem];
        const updatedCustomProblems = [
          ...profileData?.customProblems,
          trimmedProblem,
        ];
        dispatch(setProblems(updatedProblems));
        dispatch(setCustomProblems(updatedCustomProblems));
        setCustomProblem('');
      } else {
        setProblemsLimit(true);
      }
    }
  };

  const handleTermsAccepted = () => {
    const event = {
      code: '#PB6',
      createdAt: new Date().toISOString(),
      attributes: {
        currentScreen: 'termsModal',
        screenTime: (new Date() - screenTime) / 1000,
      },
    };
    dispatch(
      addProfileBuildingEvent({
        code: event.code,
        value: event,
      }),
    );
    InteractionManager.runAfterInteractions(() => {
      dispatch(setPrivacyTermsAccepted(true));
      navigation.replace('SignUp');
      setShowTermsModal(false);
    });
  };

  const renderProblemItem = ({ item }) => (
    <View style={styles.renderProblemItem}>
      <TouchableOpacity
        style={[
          styles.healthConcernOption,
          profileData?.problems.includes(item)
            ? styles.selectedHealthConcern
            : null,
        ]}
        onPress={() => handleProblemsChange(item)}
      >
        <Heading
          text={item}
          size="sm"
          color={
            profileData?.problems.includes(item)
              ? 'white'
              : theme.colors.placeholderTextColor
          }
        />
      </TouchableOpacity>
    </View>
  );

  const renderSelectedProblem = (concern, index) => (
    <View key={index} style={styles.selectedConcernTag}>
      <TouchableOpacity onPress={() => handleProblemsChange(concern)}>
        <IconButton
          icon="close"
          size={16}
          iconColor="white"
          style={styles.closeButtonIcon}
        />
      </TouchableOpacity>
      <Heading text={concern} size="sm" color="white" />
    </View>
  );

  const renderMainContent = () => {
    return (
      <View
        style={[
          styles.layoutContainer,
          { backgroundColor: theme.colors.background },
        ]}
      >
        <View style={[profileBuilding ? styles.topGap : styles.topProfileGap]}>
          {profileBuilding && (
            <Heading
              text={`Thanks, ${profileData?.name}!`}
              size="lg"
              fontFamily={Fonts.MEDIUM}
              color={theme.colors.text}
            />
          )}
          <Heading
            text="What health concerns troubling you the most?"
            fontSize={32}
            fontFamily={Fonts.MEDIUM}
            color={theme.colors.text}
          />
        </View>
        {profileData?.problems.length ? (
          <View style={styles.selectedConcernsContainer}>
            {profileData?.problems.map((concern, index) =>
              renderSelectedProblem(concern, index),
            )}
          </View>
        ) : (
          <View style={styles.gap} />
        )}
      </View>
    );
  };

  const renderFooterComponent = useMemo(
    () => (
      <View style={styles.customProblemInputContainer}>
        <Input
          containerStyle={{ flex: 1 }}
          value={customProblem}
          onChangeText={setCustomProblem}
          placeholder="Add your own"
          onSubmitEditing={handleCustomProblem}
          returnKeyType="done"
          editable={profileData?.problems.length < 3}
          textAlign="center"
          hasValue={!!customProblem}
          style={styles.forInputFieldMargin}
        />
      </View>
    ),
    [customProblem, profileData?.problems.length],
  );

  const handleNextPress = async () => {
    const createdAt = new Date().toISOString();
    if (!profileBuilding) {
      setIsLoading(true);
      const updatedProfile = {
        ...profileData,
      };
      try {
        await updateUserProfile(updatedProfile);
      } catch (error) {
        console.error('Error updating goals:', error);
      }
      const attributes = {
        currentScreen: 'problems',
        screenTime: (new Date() - screenTime) / 1000,
      };
      await recordEvent(
        'Profile Event',
        profileData.username,
        true,
        '#P1',
        attributes,
        createdAt,
      );
      navigation.navigate('Settings');
      setIsLoading(false);
      return;
    }
    const event = {
      code: '#PB5',
      createdAt: createdAt,
      attributes: {
        currentScreen: 'problems',
        screenTime: (new Date() - screenTime) / 1000,
      },
    };
    dispatch(
      addProfileBuildingEvent({
        code: event.code,
        value: event,
      }),
    );
    setScreenTime(new Date());
    setIsLoading(true);
    Keyboard.dismiss();
    setShowTermsModal(true);
  };

  const handleBackPress = () => {
    dispatch(setProblems(oldProblems));
    dispatch(setCustomProblems(oldCustomProblems));
    if (!profileBuilding) {
      navigation.navigate('Settings');
      return;
    }
    navigation.navigate('Step3');
  };

  return (
    <SafeAreaView
      style={[
        styles.profileBuilderContainer,
        { backgroundColor: theme.colors.background },
      ]}
    >
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={styles.profileBuilderContainer}>
            <View style={styles.profileBuilderScrollContainer}>
              {profileBuilding && (
                <View style={styles.progressBar}>
                  <ProgressBar step={4} />
                </View>
              )}
              <View style={{ flex: 1 }}>
                <View pointerEvents={isLoading ? 'none' : 'auto'}>
                  <FlatList
                    key={'numColumns-2'}
                    data={healthConcernsOptions}
                    keyExtractor={(item, index) => index.toString()}
                    renderItem={renderProblemItem}
                    ListHeaderComponent={renderMainContent}
                    ListFooterComponent={renderFooterComponent}
                    keyboardShouldPersistTaps="handled"
                    extraData={profileData?.problems}
                    numColumns={2}
                    removeClippedSubviews={false}
                  />
                </View>

                <WarningModal
                  visible={problemsLimit}
                  text="YPD allows tracking of up to 3 goals. Please adjust your selection accordingly."
                  onClose={() => setProblemsLimit(false)}
                  icon="alert-outline"
                  size={30}
                  iconColor="red"
                  backgroundColor={ypdRedLight}
                />
                <TermsModal
                  visible={privacyTermsModal}
                  termsAgreed={termsAgreed}
                  setTermsAgreed={setTermsAgreed}
                  onContinue={handleTermsAccepted}
                />
              </View>
            </View>
            <View style={styles.footerSection}>
              <ProfileBuilderButton
                continueText="Next"
                onContinuePress={handleNextPress}
                continueDisabled={profileData?.problems.length < 1 || isLoading}
                continueButtonStyle={{
                  backgroundColor:
                    profileData?.problems.length < 1 ? ypdWhite : ypdOldGreen,
                }}
                activityIndicator={isLoading}
                backDisabled={isLoading}
                onBackPress={handleBackPress}
              />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default Step4;
