import { useCallback, useState, useContext } from 'react';
import {
  View,
  TouchableOpacity,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  ScrollView,
  Keyboard,
  InteractionManager,
  Platform,
} from 'react-native';
import Heading from '../../../components/Heading';
import styles from '../../../assets/styles/ProfileBuilderStyles';
import { Fonts, genders, responsiveStyles, resetState } from '../../../utils';
import { ypdOldGreen, ypdWhite } from '../../../utils/colors';
import { useDispatch, useSelector } from 'react-redux';
import {
  setGender,
  setSafetyCheckAccepted,
} from '../../../redux/slices/profileSlice';
import ProgressBar from '../progressBar';
import SafetyModal from '../../profileBuilding/safetyModal';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';
import ProfileBuilderButton from '../../../components/ProfileBuilderButton';
import { resetLoadingState } from '../../../utils';
import { addProfileBuildingEvent } from '../../../redux/slices/eventsSlice';
import { ThemeContext } from '../../../context/ThemeContext';

const Step3 = () => {
  const { theme } = useContext(ThemeContext);
  const dispatch = useDispatch();
  const navigation = useNavigation();

  const [screenTime, setScreenTime] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [safetyModal, setSafetyModal] = useState(false);

  useFocusEffect(
    useCallback(() => {
      setScreenTime(new Date());
    }, []),
  );

  resetLoadingState(setIsLoading);
  const { name: username, gender: userGender } = useSelector(
    (state) => state.profile,
  );

  const handleSafetyAgreed = () => {
    const event = {
      code: '#PB4',
      createdAt: new Date().toISOString(),
      attributes: {
        currentScreen: 'safetyTerms',
        screenTime: (new Date() - screenTime) / 1000,
      },
    };
    dispatch(
      addProfileBuildingEvent({
        code: event.code,
        value: event,
      }),
    );

    InteractionManager.runAfterInteractions(() => {
      dispatch(setSafetyCheckAccepted(true));
      navigation.replace('Step4');
      setSafetyModal(false);
    });
  };

  const handleSafetyDisagreed = async () => {
    console.log('User did not confirm: State cleared');
    navigation.replace('WarningScreen');
    setSafetyModal(false);
    setIsLoading(false);
    await resetState();
  };

  const handleNextPress = () => {
    const event = {
      code: '#PB3',
      createdAt: new Date().toISOString(),
      attributes: {
        currentScreen: 'gender',
        screenTime: (new Date() - screenTime) / 1000,
      },
    };
    dispatch(
      addProfileBuildingEvent({
        code: event.code,
        value: event,
      }),
    );
    setScreenTime(new Date());

    setIsLoading(true);
    Keyboard.dismiss();
    setSafetyModal(true);
  };

  return (
    <SafeAreaView
      style={[
        styles.profileBuilderContainer,
        { backgroundColor: theme.colors.background },
      ]}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={{ flex: 1 }}>
            <View style={styles.progressBar}>
              <ProgressBar step={3} />
            </View>
            <ScrollView
              contentContainerStyle={[
                styles.profileBuilderScrollContainer,
                { paddingTop: responsiveStyles.progressBarPaddingTop },
              ]}
            >
              <View style={styles.wrapper}>
                <View style={styles.content}>
                  <Heading
                    text="To make sure we recommend the right type of guidance for you"
                    fontSize={responsiveStyles.fontSize}
                    fontFamily={Fonts.REGULAR}
                    color={theme.colors.text}
                  />
                  <Heading
                    text={`${username}, What is your gender?`}
                    size="lg"
                    fontFamily={Fonts.MEDIUM}
                    color={theme.colors.text}
                  />
                </View>
                <View style={styles.radioContainer}>
                  {genders.map((gender, index) => (
                    <TouchableOpacity
                      key={index}
                      style={[
                        styles.radio,
                        userGender === gender ? styles.selectedRadio : null,
                      ]}
                      onPress={() => dispatch(setGender(gender))}
                    >
                      <Heading
                        text={gender}
                        size="sm"
                        style={[
                          userGender === gender
                            ? styles.selectedRadioText
                            : styles.radioText,
                        ]}
                        color={theme.colors.placeholderTextColor}
                      />
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
              <SafetyModal
                visible={safetyModal}
                onConfirm={handleSafetyAgreed}
                onDecline={handleSafetyDisagreed}
                navigation={navigation}
              />
              <View style={styles.bottomButton}>
                <ProfileBuilderButton
                  continueText="Next"
                  onContinuePress={handleNextPress}
                  continueDisabled={!userGender || isLoading}
                  continueButtonStyle={{
                    backgroundColor: userGender ? ypdOldGreen : ypdWhite,
                    borderWidth: userGender ? 0 : 1,
                    borderColor: userGender ? 'transparent' : ypdOldGreen,
                  }}
                  onBackPress={() => navigation.navigate('Step2')}
                />
              </View>
            </ScrollView>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default Step3;
