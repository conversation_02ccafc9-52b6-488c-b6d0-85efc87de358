import { useCallback, useState, useContext } from 'react';
import {
  View,
  TouchableOpacity,
  KeyboardAvoidingView,
  Keyboard,
  ScrollView,
  TouchableWithoutFeedback,
  Platform,
} from 'react-native';
import Heading from '../../../components/Heading';
import styles from '../../../assets/styles/ProfileBuilderStyles';
import { ages, Fonts, responsiveStyles } from '../../../utils';
import { ypdOldGreen, ypdWhite } from '../../../utils/colors';
import { useDispatch, useSelector } from 'react-redux';
import { setAge } from '../../../redux/slices/profileSlice';
import ProgressBar from '../../profileBuilding/progressBar';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';
import ProfileBuilderButton from '../../../components/ProfileBuilderButton';
import { resetLoadingState } from '../../../utils';
import { addProfileBuildingEvent } from '../../../redux/slices/eventsSlice';
import { ThemeContext } from '../../../context/ThemeContext';

const Step2 = () => {
  const { theme } = useContext(ThemeContext);
  const dispatch = useDispatch();
  const navigation = useNavigation();

  const [screenTime, setScreenTime] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const { name, age: userAge } = useSelector((state) => state.profile);

  useFocusEffect(
    useCallback(() => {
      setScreenTime(new Date());
    }, []),
  );

  resetLoadingState(setIsLoading);

  const handleNextPress = () => {
    const event = {
      code: '#PB2',
      createdAt: new Date().toISOString(),
      attributes: {
        currentScreen: 'age',
        screenTime: (new Date() - screenTime) / 1000,
      },
    };
    dispatch(
      addProfileBuildingEvent({
        code: event.code,
        value: event,
      }),
    );

    setIsLoading(true);
    Keyboard.dismiss();
    navigation.navigate('Step3');
  };

  return (
    <SafeAreaView
      style={[
        styles.profileBuilderContainer,
        { backgroundColor: theme.colors.background },
      ]}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={{ flex: 1 }}>
            <View style={styles.progressBar}>
              <ProgressBar step={2} />
            </View>
            <ScrollView
              contentContainerStyle={[
                styles.profileBuilderScrollContainer,
                { paddingTop: responsiveStyles.progressBarPaddingTop },
              ]}
            >
              <View style={styles.wrapper}>
                <View style={styles.content}>
                  <Heading
                    text="To make sure we recommend the right type of guidance for you"
                    fontSize={responsiveStyles.fontSize}
                    fontFamily={Fonts.REGULAR}
                    color={theme.colors.text}
                  />
                  <Heading
                    text={`${name}, what is your age?`}
                    size="lg"
                    fontFamily={Fonts.MEDIUM}
                    color={theme.colors.text}
                  />
                </View>
                <View style={styles.radioContainer}>
                  {ages.map((age, index) => (
                    <TouchableOpacity
                      key={index}
                      style={[
                        styles.radio,
                        userAge === age ? styles.selectedRadio : null,
                      ]}
                      onPress={() => dispatch(setAge(age))}
                    >
                      <Heading
                        text={age}
                        size="sm"
                        style={[
                          userAge === age
                            ? styles.selectedRadioText
                            : styles.radioText,
                        ]}
                        color={theme.colors.placeholderTextColor}
                      />
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
              <View style={styles.bottomButton}>
                <ProfileBuilderButton
                  continueText={userAge ? 'Next' : 'Skip'}
                  onContinuePress={handleNextPress}
                  continueDisabled={isLoading}
                  continueButtonStyle={{
                    backgroundColor: userAge ? ypdOldGreen : ypdWhite,
                    borderWidth: userAge ? 0 : 1,
                    borderColor: userAge ? 'transparent' : ypdOldGreen,
                  }}
                  onBackPress={() => navigation.navigate('Step1')}
                />
              </View>
            </ScrollView>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default Step2;
