import { useCallback, useState, useContext } from 'react';
import {
  View,
  TouchableWithoutFeedback,
  ScrollView,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import Heading from '../../../components/Heading';
import Input from '../../../components/Input';
import styles from '../../../assets/styles/ProfileBuilderStyles';
import { Fonts, responsiveStyles } from '../../../utils';
import { useDispatch, useSelector } from 'react-redux';
import { setName } from '../../../redux/slices/profileSlice';
import ProgressBar from '../progressBar';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';
import ProfileBuilderButton from '../../../components/ProfileBuilderButton';
import { resetLoadingState } from '../../../utils';
import { addProfileBuildingEvent } from '../../../redux/slices/eventsSlice';
import { ThemeContext } from '../../../context/ThemeContext';

const Step1 = () => {
  const { theme } = useContext(ThemeContext);
  const dispatch = useDispatch();
  const navigation = useNavigation();

  const [screenTime, setScreenTime] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  useFocusEffect(
    useCallback(() => {
      setScreenTime(new Date());
    }, []),
  );

  const name = useSelector((state) => state.profile.name) || '';

  resetLoadingState(setIsLoading);

  const handleNextPress = async () => {
    const event = {
      code: '#PB1',
      createdAt: new Date().toISOString(),
      attributes: {
        isTriggered: true,
        currentScreen: 'name',
        screenTime: (new Date() - screenTime) / 1000,
      },
    };
    dispatch(
      addProfileBuildingEvent({
        code: event.code,
        value: event,
      }),
    );

    setIsLoading(true);
    Keyboard.dismiss();
    navigation.navigate('Step2');
  };

  return (
    <SafeAreaView
      style={[
        styles.profileBuilderContainer,
        { backgroundColor: theme.colors.background },
      ]}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={{ flex: 1 }}>
            <View style={styles.progressBar}>
              <ProgressBar step={1} />
            </View>
            <ScrollView
              keyboardShouldPersistTaps="handled"
              contentContainerStyle={[
                styles.profileBuilderScrollContainer,
                { paddingTop: responsiveStyles.progressBarPaddingTop },
              ]}
            >
              <View style={styles.wrapper}>
                <View style={styles.content}>
                  <Heading
                    text="Let's build your personalized profile to track symptoms and health
                  goals, making it easier to find what brings you the most relief."
                    fontFamily={Fonts.REGULAR}
                    fontSize={responsiveStyles.fontSize}
                    color={theme.colors.text}
                  />
                  <Heading
                    text="What is your name?"
                    size="lg"
                    fontFamily={Fonts.MEDIUM}
                    color={theme.colors.text}
                  />
                </View>
                <Input
                  placeholder="Tap to type here"
                  value={name}
                  onChangeText={(text) => dispatch(setName(text))}
                  hasValue={!!name}
                />
              </View>
              <View style={styles.bottomButton}>
                <ProfileBuilderButton
                  continueText="Next"
                  onContinuePress={handleNextPress}
                  continueDisabled={!name || isLoading}
                  onBackPress={() => navigation.goBack()}
                />
              </View>
            </ScrollView>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default Step1;
