import { useContext } from 'react';
import {
  View,
  TouchableWithoutFeedback,
  ScrollView,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import Heading from '../../../components/Heading';
import styles from '../../../assets/styles/ProfileBuilderStyles';
import { Fonts, responsiveStyles } from '../../../utils';
import ProgressBar from '../progressBar';
import { SafeAreaView } from 'react-native-safe-area-context';
import Dose from '../../../components/Dose';
import { ThemeContext } from '../../../context/ThemeContext';

const Step5 = () => {
  const { theme } = useContext(ThemeContext);
  return (
    <SafeAreaView
      style={[
        styles.profileBuilderContainer,
        { backgroundColor: theme.colors.background },
      ]}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={{ flex: 1 }}>
            <View style={styles.progressBar}>
              <ProgressBar step={5} />
            </View>
            <ScrollView
              keyboardShouldPersistTaps="handled"
              keyboardDismissMode="on-drag"
              contentContainerStyle={[
                styles.profileBuilderScrollContainer,
                { paddingTop: responsiveStyles.progressBarPaddingTop },
              ]}
            >
              <View>
                <Heading
                  text="How much cannabis do you currently use?"
                  fontFamily={Fonts.MEDIUM}
                  fontSize={31}
                  color={theme.colors.text}
                />
                <Dose />
              </View>
            </ScrollView>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default Step5;
