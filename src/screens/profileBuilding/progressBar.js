import { useEffect, useRef, useContext } from 'react';
import { View, Animated } from 'react-native';
import styles from '../../assets/styles/ProgressBar.scss';
import Heading from '../../components/Heading';
import { Fonts } from '../../utils';
import { ThemeContext } from '../../context/ThemeContext';

const ProgressBar = ({ step }) => {
  const { theme } = useContext(ThemeContext);

  const totalSteps = 7;
  const progressWidth = (step / totalSteps) * 100;
  const animatedWidth = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(animatedWidth, {
      toValue: progressWidth,
      duration: 500,
      useNativeDriver: false,
    }).start();
  }, [step]);

  return (
    <View
      style={[styles.container, { backgroundColor: theme.colors.background }]}
    >
      <View style={styles.header}>
        <Heading
          text="Building your profile"
          size="sm"
          fontFamily={Fonts.REGULAR}
          color={theme.colors.text}
          style={styles.title}
        />

        <Heading
          text={`${step}/${totalSteps}`}
          size="sm"
          style={styles.stepIndicator}
          color={theme.colors.darkTextGray}
          fontFamily={Fonts.MEDIUM}
        />
      </View>
      <View style={styles.progressContainer}>
        <Animated.View
          style={[
            styles.progressFill,
            {
              width: animatedWidth.interpolate({
                inputRange: [0, 100],
                outputRange: ['0%', '100%'],
              }),
            },
          ]}
        />
      </View>
    </View>
  );
};

export default ProgressBar;
