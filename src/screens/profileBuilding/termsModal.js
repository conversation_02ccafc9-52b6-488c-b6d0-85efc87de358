import { useState, useContext } from 'react';
import {
  View,
  TouchableOpacity,
  ScrollView,
  Modal,
  Linking,
} from 'react-native';
import CustomButton from '../../components/CustomButton';
import styles from '../../assets/styles/TermsModalStyles';
import Heading from '../../components/Heading';
import { Fonts } from '../../utils';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { policiesConditions } from '../../utils';
import { ypdBlack, ypdGreen, ypdWhite } from '../../utils/colors';
import { ThemeContext } from '../../context/ThemeContext';

const TermsModal = ({ visible, termsAgreed, setTermsAgreed, onContinue }) => {
  const { theme } = useContext(ThemeContext);

  const [isLoading, setIsLoading] = useState(false);

  const openLink = (url) => {
    Linking.openURL(url);
  };

  const handleContinue = async () => {
    setIsLoading(true);

    try {
      await onContinue();
    } catch (error) {
      console.error('Error in onContinue:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal visible={visible} animationType="slide" transparent={false}>
      <View style={styles.mainContainer}>
        <ScrollView
          contentContainerStyle={[
            styles.modalContent,
            { backgroundColor: theme.colors.background },
          ]}
        >
          <View style={styles.modalHeader}>
            <Heading
              text="Confirm you are ready"
              size="lg"
              fontFamily={Fonts.MEDIUM}
              style={{ marginBottom: 30 }}
              color={theme.colors.text}
            />
            <Heading
              text="YPD Privacy Policy"
              size="sm"
              fontFamily={Fonts.MEDIUM}
              style={{ marginBottom: 3 }}
              color={theme.colors.text}
            />
            <View style={styles.inlineTextContainer}>
              <Heading
                text="Please read our "
                size="sm"
                fontFamily={Fonts.MEDIUM}
                style={styles.modalDescription}
                color={theme.colors.text}
              />
              <TouchableOpacity
                onPress={() => openLink('https://yourperfectdose.com/privacy')}
              >
                <Heading
                  text="Privacy Policy"
                  size="sm"
                  fontFamily={Fonts.MEDIUM}
                  style={styles.link}
                  color={theme.colors.text}
                />
              </TouchableOpacity>
              <Heading
                text=" carefully to "
                size="sm"
                fontFamily={Fonts.MEDIUM}
                style={styles.modalDescription}
                color={theme.colors.text}
              />
              <Heading
                text="understand our policies and practices regarding your information and how we will treat it."
                size="sm"
                fontFamily={Fonts.MEDIUM}
                style={styles.modalDescription}
                color={theme.colors.text}
              />
            </View>
          </View>
          <View style={styles.termsWrapper}>
            <Heading
              text="Please review and agree to the YPD Terms of Service:"
              size="sm"
              fontFamily={Fonts.BOLD}
              color={theme.colors.text}
            />
            {policiesConditions.map((condition, index) => (
              <View key={index} style={styles.conditionContainer}>
                <Heading
                  text="•"
                  size="md"
                  style={styles.bullet}
                  color={theme.colors.text}
                />
                <Heading
                  text={condition}
                  size="xsmall"
                  fontFamily={Fonts.MEDIUM}
                  color={theme.colors.text}
                />
              </View>
            ))}
            <View style={styles.inlineTextContainer}>
              <Heading
                text="Please read our full "
                size="sm"
                fontFamily={Fonts.MEDIUM}
                style={styles.modalDescription}
                color={theme.colors.text}
              />
              <TouchableOpacity
                onPress={() => openLink('https://yourperfectdose.com/privacy')}
              >
                <Heading
                  text="Terms of Service (TOS)"
                  size="sm"
                  fontFamily={Fonts.MEDIUM}
                  style={styles.link}
                  color={theme.colors.text}
                />
              </TouchableOpacity>
            </View>
          </View>
          <View style={styles.agreeSectionWrapper}>
            <View style={styles.divider} />
            <View style={styles.agreeSectionInsideWrapper}>
              <TouchableOpacity
                style={styles.checkboxContainer}
                onPress={() => setTermsAgreed(!termsAgreed)}
              >
                <View
                  style={[
                    styles.checkbox,
                    termsAgreed && styles.checkboxChecked,
                  ]}
                >
                  {termsAgreed && (
                    <MaterialIcons name="check" size={22} color={ypdWhite} />
                  )}
                </View>
                <View style={styles.inlineTextContainer}>
                  <Heading
                    text="I read and agree to Your Perfect Dose "
                    size="xsmall"
                    fontFamily={Fonts.MEDIUM}
                    style={styles.checkboxText}
                    color={theme.colors.text}
                  />
                  <TouchableOpacity
                    onPress={() =>
                      openLink('https://yourperfectdose.com/privacy')
                    }
                  >
                    <Heading
                      text="Privacy Policy"
                      size="sm"
                      fontFamily={Fonts.MEDIUM}
                      style={styles.link}
                      color={theme.colors.text}
                    />
                  </TouchableOpacity>
                  <Heading
                    text=" & "
                    size="sm"
                    fontFamily={Fonts.MEDIUM}
                    style={styles.checkboxText}
                    color={theme.colors.text}
                  />
                  <TouchableOpacity
                    onPress={() =>
                      openLink('https://yourperfectdose.com/terms-of-service')
                    }
                  >
                    <Heading
                      text="Terms of Use"
                      size="sm"
                      fontFamily={Fonts.MEDIUM}
                      style={styles.link}
                      color={theme.colors.text}
                    />
                  </TouchableOpacity>
                </View>
              </TouchableOpacity>
            </View>
            <CustomButton
              title="Continue"
              onPress={handleContinue}
              disabled={isLoading || !termsAgreed}
              style={{
                backgroundColor: termsAgreed ? ypdGreen : ypdWhite,
              }}
              textStyle={{
                color: termsAgreed ? ypdBlack : ypdBlack,
              }}
            />
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

export default TermsModal;
