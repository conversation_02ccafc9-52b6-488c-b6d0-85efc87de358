import { ypdTextGrey, ypdLink, ypdRed } from './colors';
import DeviceInfo from 'react-native-device-info';
import Struggling from '../assets/svgs/checkin/Struggling';
import NotGreat from '../assets/svgs/checkin/NotGreat';
import Mild from '../assets/svgs/checkin/Mild';
import Smiley from '../assets/svgs/checkin/Smiley';
import UnHappy from '../assets/svgs/checkin/UnHappy';
import { responsiveStyles } from '../utils';

export const validateInputs = (
  email = '',
  validateEmail = false,
  password = '',
  validatePassword = false,
  code = '',
  validateCode = false,
) => {
  if (validateEmail) {
    if (!email.trim()) {
      return 'Please enter an email address!';
    }

    if (email.includes(' ')) {
      return 'Email cannot contain spaces!';
    }

    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (!emailPattern.test(email)) {
      return 'Please enter a valid email address!';
    }
  }

  if (validatePassword) {
    if (!password.trim()) {
      return 'Please enter a password!';
    }

    if (password.includes(' ')) {
      return 'Password cannot contain spaces!';
    }

    if (password.length < 8) {
      return 'Password must be at least 8 characters long!';
    }
  }

  if (validateCode) {
    if (!code.trim()) {
      return 'Please enter a code!';
    }

    if (code.includes(' ')) {
      return 'Code cannot contain spaces!';
    }

    if (!/^\d+$/.test(code)) {
      return 'Code must contain only digits.';
    }

    if (!/^\d{6}$/.test(code)) {
      return 'Please enter a 6-digit confirmation code.';
    }
  }

  return '';
};

export const parseAWSAuthError = (error) => {
  let errorMessage = 'An error occurred. Please try again.';
  if (error.name === 'UserNotFoundException') {
    errorMessage = 'User not found. Please check your email.';
  } else {
    const raw = error.toString();
    if (raw.includes(':')) {
      errorMessage = raw.split(':')[1].trim();
    }
  }

  return errorMessage;
};

export const passwordChecker = (focusedInput, password) => {
  if (focusedInput !== 'newPassword' && focusedInput !== 'password')
    return ypdTextGrey;
  if (password.length === 0) return ypdLink;
  if (password.length < 8) return ypdRed;
  return ypdLink;
};

export const ratingInfo = [
  {},
  {
    label: 'The worst!',
    image: (
      <UnHappy
        height={responsiveStyles.svgIconSize}
        width={responsiveStyles.svgIconSize}
      />
    ),
  },
  {
    label: 'Really struggling',
    image: (
      <Struggling
        height={responsiveStyles.svgIconSize}
        width={responsiveStyles.svgIconSize}
      />
    ),
  },
  {
    label: 'Not great',
    image: (
      <NotGreat
        height={responsiveStyles.svgIconSize}
        width={responsiveStyles.svgIconSize}
      />
    ),
  },
  {
    label: 'Mild discomfort',
    image: (
      <Mild
        height={responsiveStyles.svgIconSize}
        width={responsiveStyles.svgIconSize}
      />
    ),
  },
  {
    label: 'All good!',
    image: (
      <Smiley
        height={responsiveStyles.svgIconSize}
        width={responsiveStyles.svgIconSize}
      />
    ),
  },
];

export const getDevicePlatform = () => {
  const osName = DeviceInfo.getSystemName();
  const osVersion = DeviceInfo.getSystemVersion();
  return `${osName} (${osVersion})`;
};

const numberWords = [
  'Zero',
  'One',
  'Two',
  'Three',
  'Four',
  'Five',
  'Six',
  'Seven',
];

export const toWords = (n) => {
  return numberWords[n] || `${n}`;
};

export const getCurrentWeekRange = () => {
  const today = new Date();
  const day = today.getDay(); // Sunday = 0
  const monday = new Date(today);
  monday.setDate(today.getDate() - (day === 0 ? 6 : day - 1));
  const sunday = new Date(monday);
  sunday.setDate(monday.getDate() + 6);

  const options = { month: 'long', day: 'numeric' };

  const startStr = monday.toLocaleDateString('en-US', options);
  const endStr = sunday.getDate(); // only day for end

  return `${startStr} - ${endStr}`;
};

export const getStartOfWeek = (date) => {
  const d = new Date(date);
  const day = d.getDay(); // Sunday = 0, Monday = 1, ..., Saturday = 6
  const diff = d.getDate() - day + (day === 0 ? -6 : 1); // adjust when Sunday
  return new Date(d.setDate(diff));
};

export const getMonday = (dateStr) => {
  const date = new Date(dateStr);
  const day = date.getDay();
  const diff = date.getDate() - day + (day === 0 ? -6 : 1);
  const monday = new Date(date.setDate(diff));
  monday.setHours(0, 0, 0, 0);
  return monday.toISOString().split('T')[0];
};
