import { useState, useEffect, useRef, useContext } from 'react';
import {
  PermissionsAndroid,
  Platform,
  Linking,
  AppState,
  View,
  Animated,
} from 'react-native';
import Geolocation from 'react-native-geolocation-service';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { recordEvent } from '../api/events';
import styles from '../../styles.scss';
import WarningScreen from '../screens/warning/warningScreen';
import App from '../app';
import { AuthContext } from '../context/AuthContext';
import SplashLogo from '../screens/splash/splashLogo';

const allowedCountries = process.env.ALLOWED_COUNTRIES;
const allowedRegions = JSON.parse(process.env.ALLOWED_REGIONS);
const MAX_RETRIES = 3;

export const Geofencing = () => {
  const { isAuthenticated, isLoading } = useContext(AuthContext);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  const [appState, setAppState] = useState(AppState.currentState);
  const [errorCode, setErrorMessage] = useState(null);
  const [initialRoute, setInitialRoute] = useState(null);
  const [regionMessage, setRegionMessage] = useState('');
  const [permissionGranted, setPermissionGranted] = useState(null);
  const [isInAllowedRegion, setIsInAllowedRegion] = useState(null);
  const [showSettingsAlert, setShowSettingsAlert] = useState(false);
  const [isSplashVisible, setIsSplashVisible] = useState(true);
  const [isCheckingLocation, setIsCheckingLocation] = useState(true);
  const [hasInitialized, setHasInitialized] = useState(false);

  useEffect(() => {
    const initApp = async () => {
      const splashPromise = new Promise((resolve) => setTimeout(resolve, 1500));
      await splashPromise;

      if (!isLoading && !isCheckingLocation) {
        setInitialRoute(isAuthenticated ? 'Tabs' : 'MainIntro');
        setIsSplashVisible(false);
        setHasInitialized(true);
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }).start();
      }
    };

    initApp();
  }, [isLoading, isAuthenticated, isCheckingLocation]);

  const checkLocationPermission = async () => {
    console.log('Checking Location Permission');
    try {
      let granted;
      if (Platform.OS === 'android') {
        granted = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        );
      } else {
        granted =
          (await Geolocation.requestAuthorization('whenInUse')) === 'granted';
      }
      console.log('Permission:', granted);
      setPermissionGranted(granted);
      return granted;
    } catch (error) {
      console.log('Permission check error:', error, 'Error Code: YPDE001');
      await recordEventFailure(error.message, 'YPDE001');
      setIsCheckingLocation(false);
      return false;
    }
  };

  const requestLocationPermission = async () => {
    console.log('Requesting Location Permission');
    try {
      let granted;
      if (Platform.OS === 'android') {
        granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            title: 'Location Access Required',
            message: 'This app needs to access your location for working.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          },
        );
        if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
          setShowSettingsAlert(true);
          setIsCheckingLocation(false);
          return granted;
        }
      } else {
        granted = await Geolocation.requestAuthorization('whenInUse');
      }
      console.log('Permission Access:', granted);

      if (
        granted === PermissionsAndroid.RESULTS.GRANTED ||
        granted === 'granted'
      ) {
        setPermissionGranted(true);
        return true;
      } else {
        setPermissionGranted(false);
        setIsCheckingLocation(false);
        return false;
      }
    } catch (error) {
      console.log('Permission request error:', error, 'Error Code: YPDE002');
      await recordEventFailure(error.message, 'YPDE002');
      setIsCheckingLocation(false);
      return false;
    }
  };

  const getCurrentLocation = async (retryCount) => {
    console.log('Checking location coordinates...');

    const getPosition = () => {
      return new Promise((resolve, reject) => {
        Geolocation.getCurrentPosition(
          (position) => resolve(position),
          (error) => reject(error),
          { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 },
        );
      });
    };

    try {
      const position = await getPosition();
      const { latitude, longitude } = position.coords;
      const roundedLatitude = parseFloat(latitude.toFixed(3));
      const roundedLongitude = parseFloat(longitude.toFixed(3));

      const regionAllowed = await AsyncStorage.getItem('regionAllowed');
      const prevLatitude = parseFloat(await AsyncStorage.getItem('latitude'));
      const prevLongitude = parseFloat(await AsyncStorage.getItem('longitude'));

      console.log('Previous coordinates:', prevLatitude, prevLongitude);
      console.log('Current coordinates:', roundedLatitude, roundedLongitude);
      console.log('Region allowed (previous):', regionAllowed);

      if (
        prevLatitude !== roundedLatitude ||
        prevLongitude !== roundedLongitude ||
        regionAllowed !== 'true'
      ) {
        await AsyncStorage.setItem('latitude', roundedLatitude?.toString());
        await AsyncStorage.setItem('longitude', roundedLongitude?.toString());

        console.log('Coordinates changed');
        await checkLocationValidity(latitude, longitude, 1);
      } else {
        console.log('Coordinates not changed');
        if (regionAllowed === 'true') {
          setIsInAllowedRegion(true);
        } else {
          setIsInAllowedRegion(false);
        }
        setIsCheckingLocation(false);
      }
    } catch (error) {
      if (retryCount >= MAX_RETRIES) {
        setErrorMessage(
          'Failed to get location coordinates after several attempts. \n\nError code: YPDE003' +
            '\n\<NAME_EMAIL> and provide this error code.\n\n',
        );
        console.log(
          'Failed to get location coordinates after several attempts:',
          error,
          'Error Code: YPDE003',
        );
        await recordEventFailure(error.message, 'YPDE003');
      } else {
        console.log(`Retrying... (${retryCount + 1}/${MAX_RETRIES})`);
        await getCurrentLocation(retryCount + 1);
      }
    }
  };

  const checkLocationValidity = async (latitude, longitude, retryCount) => {
    console.log('Checking if in allowed regions...', new Date());

    try {
      const response = await fetch(
        `https://api.opencagedata.com/geocode/v1/json?q=${latitude}+${longitude}&key=${process.env.GEOCODING_API_KEY}`,
      );

      if (!response.ok) {
        throw new Error(`Geocoding API error: ${response.status}`);
      }

      const data = await response.json();

      if (data.results.length > 0) {
        const {
          country,
          state,
          state_code: stateCode,
        } = data.results[0].components;
        console.log('Detected country:', country);
        console.log('Detected state:', state);
        console.log("Detected state's code:", stateCode);

        await AsyncStorage.setItem('country', country || 'unknown');
        await AsyncStorage.setItem('state', state || 'unknown');

        if (!country || !state) {
          throw new Error('Incomplete address components');
        }

        const isAllowedCountry = allowedCountries.includes(
          country.toLowerCase(),
        );
        const inAllowedRegion = allowedRegions.some(
          (region) =>
            region.state.toLowerCase() === state.toLowerCase() ||
            region.code === stateCode,
        );

        console.log('Is allowed country:', isAllowedCountry);
        console.log('Is in allowed region:', inAllowedRegion);

        const isAllowed = isAllowedCountry && inAllowedRegion;
        setIsInAllowedRegion(isAllowed);

        if (isAllowed) {
          console.log('User is in the allowed region.');
          setRegionMessage('');
          await AsyncStorage.setItem('regionAllowed', 'true');
        } else {
          console.log('User is not in the allowed region.');
          const message = `Sorry, this app is not available in ${state}, ${country}.`;
          setRegionMessage(message);
          await AsyncStorage.setItem('regionAllowed', 'false');
        }
        await recordEventSuccess(data.results[0]);
        setIsCheckingLocation(false);
      } else {
        throw new Error('No results from geocoding API');
      }
    } catch (error) {
      console.log(
        'Failed to check app availability!',
        `Attempt ${retryCount + 1} of ${MAX_RETRIES}`,
      );

      if (retryCount < MAX_RETRIES - 1) {
        await checkLocationValidity(latitude, longitude, retryCount + 1);
      } else {
        setErrorMessage(
          "Failed to check app's availability in your region after several attempts. Please make sure you have a working internet connection." +
            '\n\nIf the problem still persists, <NAME_EMAIL> and provide this error code. \n\nError code: YPDE004\n\n',
        );
        console.log(
          'Failed to check app availability in your region after several attempts:',
          error,
          'Error code: YPDE004',
        );
        setIsCheckingLocation(false);
        await recordEventFailure(error.message, 'YPDE004');
      }
    }
  };

  const recordEventSuccess = async (data) => {
    const attributes = {
      country: data.components.country,
      state: data.components.state,
      region: data.components.region,
    };

    await recordEvent(
      'Geofencing Event',
      null,
      true,
      '#G1',
      attributes,
      new Date().toISOString(),
    );
  };

  const recordEventFailure = async (message, code) => {
    const coordinates = `${await AsyncStorage.getItem('latitude')}, ${await AsyncStorage.getItem('longitude')}`;
    const state = await AsyncStorage.getItem('state');
    const country = await AsyncStorage.getItem('country');

    const attributes = {
      code: code,
      message: message,
      state: state,
      country: country,
      coordinates: coordinates,
    };

    await recordEvent(
      'Geofencing Event',
      null,
      false,
      '#G1',
      attributes,
      new Date().toISOString(),
    );
  };

  const getLocationPermission = async () => {
    let granted = await checkLocationPermission();

    for (let i = 0; !granted && i < 7; i++) {
      granted = await requestLocationPermission();
      if (
        granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN ||
        Platform.OS === 'ios'
      ) {
        setShowSettingsAlert(true);
        setIsCheckingLocation(false);
        break;
      }
    }
    return granted;
  };

  const openSettings = () => {
    Linking.openSettings();

    const handleAppStateChange = async (nextAppState) => {
      if (nextAppState === 'active') {
        await initGeofencingAfterReturn();
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    const timeout = setTimeout(() => {
      subscription.remove();
    }, 60000);

    return () => {
      subscription.remove();
      clearTimeout(timeout);
    };
  };

  useEffect(() => {
    const handleAppStateChange = async (nextAppState) => {
      if (appState.match(/inactive|background/) && nextAppState === 'active') {
        if (!hasInitialized || showSettingsAlert || !isInAllowedRegion) {
          console.log('App state change - Re-checking geofencing.');
          await initGeofencingAfterReturn();
        } else {
          console.log('App state change - skipping geofencing check.');
        }
      }
      setAppState(nextAppState);
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    initGeofencing();

    return () => {
      subscription.remove();
    };
  }, [hasInitialized, showSettingsAlert, isInAllowedRegion]);

  const initGeofencing = async () => {
    const granted = await getLocationPermission();
    if (granted === 'granted' || granted === true) {
      await getCurrentLocation(0);
      setShowSettingsAlert(false);
    } else {
      setIsCheckingLocation(false);
      setShowSettingsAlert(true);
    }
  };

  const initGeofencingAfterReturn = async () => {
    const granted = await checkLocationPermission();

    if (granted === 'granted' || granted === true) {
      setIsCheckingLocation(true);

      await getCurrentLocation(0);
      setShowSettingsAlert(false);
    } else {
      setIsCheckingLocation(false);
      setShowSettingsAlert(true);
    }
  };

  if (isSplashVisible || !initialRoute || isLoading || isCheckingLocation) {
    return (
      <View style={styles.container}>
        <SplashLogo />
      </View>
    );
  }

  if (showSettingsAlert && !permissionGranted) {
    return (
      <View style={styles.container}>
        <WarningScreen
          message="Location permission is required to use this app."
          onButtonPress={openSettings}
          buttonText="Open Settings"
          fromGeofencing={true}
        />
      </View>
    );
  }

  if (!isInAllowedRegion) {
    return (
      <View style={styles.container}>
        <WarningScreen
          message={
            regionMessage || 'Sorry, this app is not available in your region.'
          }
          fromGeofencing={true}
        />
      </View>
    );
  }

  if (errorCode) {
    return (
      <View style={styles.container}>
        <WarningScreen message={errorCode} fromGeofencing={true} />
      </View>
    );
  }

  return (
    <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
      <App initialRouteName={initialRoute} />
    </Animated.View>
  );
};
