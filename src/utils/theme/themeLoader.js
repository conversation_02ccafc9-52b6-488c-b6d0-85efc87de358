import AsyncStorage from '@react-native-async-storage/async-storage';
import { Appearance } from 'react-native';
import { darkTheme, lightTheme } from './themes';

export async function loadTheme() {
  try {
    const savedPreference = await AsyncStorage.getItem('user-theme');
    const themeType = savedPreference || Appearance.getColorScheme() || 'light';
    return {
      themeType,
      theme: themeType === 'dark' ? darkTheme : lightTheme,
    };
  } catch (e) {
    console.log('Error loading theme:', e);
    const fallbackType = Appearance.getColorScheme() || 'light';
    return {
      themeType: fallbackType,
      theme: fallbackType === 'dark' ? darkTheme : lightTheme,
    };
  }
}
