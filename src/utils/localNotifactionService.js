import PushNotification, { Importance } from 'react-native-push-notification';
import { Alert, Linking, PermissionsAndroid, Platform } from 'react-native';
import { store } from '../redux/store';
import { setAppStateCheck } from '../redux/slices/notificationSlice';
import { ypdOldGreen } from './colors';

class LocalNotificationService {
  constructor() {
    this.createChannels();
  }

  createChannels = () => {
    PushNotification.createChannel(
      {
        channelId: 'ypd-reminder-id',
        channelName: 'Daily Reminders',
        vibrate: true,
        importance: Importance.HIGH,
      },
      (created) => console.log(`Reminder channel created: ${created}`),
    );
    PushNotification.createChannel(
      {
        channelId: 'ypd-lockout-id',
        channelName: 'Lockout Notifications',
        vibrate: true,
        importance: Importance.HIGH,
      },
      (created) => console.log(`Lockout channel created: ${created}`),
    );
  };

  configure = async (callBack) => {
    const hasPermission =
      Platform.OS === 'ios'
        ? await this.checkNotificationPermissionIOS(false)
        : true;

    PushNotification.configure({
      onNotification: this.onNotification(callBack),
      permissions: {
        alert: true,
        badge: true,
        sound: true,
      },
      popInitialNotification: false,
      requestPermissions: Platform.OS === 'ios' ? !hasPermission : true,
    });
  };

  onNotification = (callBack) => (notification) => {
    callBack(notification);
    console.log('[LocalNotificationService] onNotification:', notification);
    if (Platform.OS === 'ios' && notification.finish) {
      notification.finish('UIBackgroundFetchResultNoData');
    }
  };

  scheduleLockoutNotification = (modality, lockoutHours) => {
    const now = new Date();
    const scheduleDate = new Date(
      now.getTime() + lockoutHours * 60 * 60 * 1000,
    );

    console.log(`Scheduling notification for ${modality} at ${scheduleDate}`);

    const message =
      'You can now take a dose or check in to get a new suggested dose';

    PushNotification.localNotificationSchedule({
      channelId: 'ypd-lockout-id',
      bigText: message,
      smallIcon: 'ic_notification',
      color: { ypdOldGreen },
      ignoreInForeground: false,
      invokeApp: true,
      title: 'Your dose is now available',
      message: message,
      userInfo: { modality },
      date: scheduleDate,
    });
  };

  requestNotificationPermissionAndroid = async (showAlert) => {
    if (Platform.Version >= 33) {
      try {
        const granted = await PermissionsAndroid.request(
          'android.permission.POST_NOTIFICATIONS',
        );
        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          console.log('Notification permission granted.');
          return true;
        } else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
          console.log('Notification permission denied with "Never ask again"');
          if (showAlert) this.showSettingsAlert();
          return false;
        } else {
          console.log('Notification permission denied.');
          if (showAlert) this.showSettingsAlert();
          return false;
        }
      } catch (err) {
        console.warn(err);
        return false;
      }
    }
    return true;
  };

  checkNotificationPermissionIOS = async (showAlert) => {
    return new Promise((resolve) => {
      PushNotification.checkPermissions((permissions) => {
        if (!permissions.alert) {
          console.log('Notification permission denied on iOS.');
          if (showAlert) this.showSettingsAlert();
          resolve(false);
        } else {
          console.log('Notification permission granted on iOS.');
          resolve(true);
        }
      });
    });
  };

  requestPermission = async (showAlert = true) => {
    if (Platform.OS === 'android') {
      return await this.requestNotificationPermissionAndroid(showAlert);
    } else if (Platform.OS === 'ios') {
      return await this.checkNotificationPermissionIOS(showAlert);
    }
    return false;
  };

  showSettingsAlert = () => {
    Alert.alert(
      'Permission Needed',
      'Notification permission is required to receive notifications. Please enable it in the app settings.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Open Settings',
          onPress: () => {
            store.dispatch(setAppStateCheck(true));
            Linking.openSettings();
          },
        },
      ],
    );
  };

  getScheduleDate = () => {
    const { morningReminder, eveningReminder } = store.getState().notifications;

    const morningDate = new Date();
    const eveningDate = new Date();

    morningDate.setHours(morningReminder.hour, morningReminder.min, 0, 0);
    eveningDate.setHours(eveningReminder.hour, eveningReminder.min, 0, 0);

    if (morningDate < new Date()) {
      morningDate.setDate(morningDate.getDate() + 1);
    }
    if (eveningDate < new Date()) {
      eveningDate.setDate(eveningDate.getDate() + 1);
    }

    return { morningDate, eveningDate };
  };

  scheduleDailyReminder = (time = 'morning') => {
    const isMorning = time === 'morning';
    const scheduleDate = this.getScheduleDate()?.[`${time}Date`];

    if (!scheduleDate) {
      console.warn(`No schedule date found for ${time} reminder`);
      return;
    }

    const title = isMorning ? 'Morning Reminder' : 'Evening Reminder';
    const message = isMorning
      ? "It's time to check in with YPD 🌅"
      : "It's time to check in with YPD 🌙";

    PushNotification.localNotificationSchedule({
      id: isMorning ? '1' : '2',
      channelId: 'ypd-reminder-id',
      title,
      message,
      date: scheduleDate,
      repeatType: 'day',
      bigText: message,
      smallIcon: 'ic_notification',
      color: ypdOldGreen,
      ignoreInForeground: false,
      invokeApp: true,
      playSound: true,
      soundName: 'default',
      vibrate: true,
      vibration: 300,
      priority: 'high',
      visibility: 'public',
      importance: 'high',
      ongoing: false,
      repeatTime: 1,
      allowWhileIdle: true,
      userInfo: {
        type: 'daily-reminder',
        reminderType: isMorning ? '1' : '2',
      },
    });

    console.log(`Scheduled ${title} for`, scheduleDate);
  };

  cancelNotification = (id) => {
    PushNotification.cancelLocalNotification(id.toString());
    console.log(`Cancelled notification with ID: ${id}`);
  };

  getScheduledNotifications = () => {
    return new Promise((resolve) => {
      PushNotification.getScheduledLocalNotifications((notifications) => {
        console.log('Scheduled notifications:', notifications);
        resolve(notifications);
      });
    });
  };

  cancelAllLocalNotifications = () => {
    PushNotification.cancelAllLocalNotifications();
    console.log('Cancelled all notifications.');
  };
}

export default new LocalNotificationService();
