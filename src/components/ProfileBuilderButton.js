import { useContext } from 'react';
import { View } from 'react-native';
import CustomButton from './CustomButton';
import BackButton from './BackButton';
import { ypdBlack, ypdOldGreen, ypdTeal, ypdWhite } from '../utils/colors';
import styles from '../assets/styles/ProfileBuilderStyles.scss';
import { ThemeContext } from '../context/ThemeContext';

const ProfileBuilderButton = ({
  onContinuePress,
  continueText = '',
  continueDisabled = false,
  continueButtonStyle = {},
  width = '30%',
  height = 40,
  variant,
  onlyBackButton = false,
  onBackPress,
  activityIndicator,
  onlyNextButton = false,
  backDisabled = false,
}) => {
  const { theme } = useContext(ThemeContext);

  const getDarkThemeButtonStyle = () => {
    if (!theme.dark) return {};

    if (continueText === 'Skip') {
      return {
        backgroundColor: 'transparent',
        borderWidth: 1,
        borderColor: ypdOldGreen,
      };
    }

    return {
      backgroundColor: continueDisabled ? ypdTeal : ypdOldGreen,
      borderWidth: 1,
      borderColor: ypdOldGreen,
    };
  };

  const getDarkThemeTextStyle = () => {
    if (!theme.dark) return {};

    if (continueText === 'Skip') {
      return {
        color: ypdWhite,
      };
    }

    return {
      color: continueDisabled ? ypdWhite : ypdTeal,
    };
  };

  return (
    <View
      style={[
        styles.profileBuilderButtonContainer,
        onlyNextButton && styles.onlyContinueButtonContainer,
      ]}
    >
      {!onlyNextButton && (
        <BackButton disabled={backDisabled} onBackPress={onBackPress} />
      )}
      {!onlyBackButton && (
        <CustomButton
          onPress={onContinuePress}
          title={continueText}
          variant={variant}
          width={width}
          height={height}
          disabled={continueDisabled}
          activityIndicator={activityIndicator}
          style={[
            continueButtonStyle,
            continueDisabled && styles.disabledButton,
            getDarkThemeButtonStyle(),
          ]}
          textStyle={[
            continueDisabled ? styles.disabledText : {},
            getDarkThemeTextStyle(),
          ]}
          color={continueDisabled ? ypdBlack : theme.dark ? undefined : variant}
        />
      )}
    </View>
  );
};

export default ProfileBuilderButton;
