import { useEffect, useState, useContext } from 'react';
import { View, Dimensions } from 'react-native';
import { IconButton } from 'react-native-paper';
import Heading from './Heading';
import { Fonts } from '../utils';
import styles from '../assets/styles/AuthWarning.scss';
import { ThemeContext } from '../context/ThemeContext';

const AuthWarning = ({
  icon,
  text,
  size = 30,
  iconColor = 'red',
  showCloseButton = true,
  onClose,
}) => {
  const [windowWidth, setWindowWidth] = useState(
    Dimensions.get('window').width,
  );
  const { theme } = useContext(ThemeContext);

  useEffect(() => {
    const updateDimensions = () => {
      setWindowWidth(Dimensions.get('window').width);
    };
    const BackHandler = Dimensions.addEventListener('change', updateDimensions);
    return () => BackHandler.remove();
  }, []);

  const boxWidth = windowWidth * 1;
  return (
    <View style={[styles.overlay]}>
      <View
        style={[
          styles.contentContainer,
          {
            backgroundColor: theme.colors.backgroundRed,
            width: boxWidth,
            maxWidth: windowWidth,
          },
        ]}
      >
        <View style={styles.wrapper}>
          <IconButton
            icon={icon}
            size={size}
            iconColor={iconColor}
            style={styles.icon}
          />
          <View style={styles.textContainer}>
            <Heading
              text={text}
              size="sm"
              fontFamily={Fonts.REGULAR}
              color={theme.colors.text}
            />
          </View>
          {showCloseButton && (
            <IconButton
              icon="close"
              style={styles.closeButton}
              size={24}
              iconColor={theme.colors.text}
              onPress={onClose}
            />
          )}
        </View>
      </View>
    </View>
  );
};

export default AuthWarning;
