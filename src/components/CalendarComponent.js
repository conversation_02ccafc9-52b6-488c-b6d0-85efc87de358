import { useState, useContext } from 'react';
import { View } from 'react-native';
import {
  Calendar,
  CalendarProvider,
  WeekCalendar,
} from 'react-native-calendars';
import { ypdGreen50, ypdBorderLight, ypdGreen } from '../utils/colors';
import { IconButton } from 'react-native-paper';
import ToggleButton from './ToggleButton';
import Heading from './Heading';
import styles from '../assets/styles/CalendarModal.scss';
import { useSelector } from 'react-redux';
import { ThemeContext } from '../context/ThemeContext';

const CalendarComponent = ({
  initialDate = new Date().toISOString().split('T')[0],
  onDateChange,
  initialViewMode = 'week',
  isLocked = true,
  onViewModeChange,
}) => {
  const { theme } = useContext(ThemeContext);
  const [viewMode, setViewMode] = useState(initialViewMode);
  const [currentDate, setCurrentDate] = useState(initialDate);
  const [selectedDate, setSelectedDate] = useState('');
  const today = new Date().toISOString().split('T')[0];

  const streakDates = useSelector(
    (state) => state.suggestions?.streakDates ?? [],
  );

  const handleGoBack = () => {
    try {
      const current = new Date(currentDate || today);
      let newDate;

      if (viewMode === 'week') {
        current.setDate(current.getDate() - 7);
        newDate = current;
      } else {
        current.setMonth(current.getMonth() - 1);
        current.setDate(1);
        newDate = current;
      }

      const newDateString = newDate.toISOString().split('T')[0];
      setCurrentDate(newDateString);
      setSelectedDate('');
    } catch (error) {
      console.error('Error navigating back:', error);
    }
  };

  const handleGoForward = () => {
    try {
      const current = new Date(currentDate || today);
      let newDate;

      if (viewMode === 'week') {
        current.setDate(current.getDate() + 7);
        newDate = current;
      } else {
        current.setMonth(current.getMonth() + 1);
        current.setDate(1);
        newDate = current;
      }

      const newDateString = newDate.toISOString().split('T')[0];
      setCurrentDate(newDateString);
      setSelectedDate('');
    } catch (error) {
      console.error('Error navigating forward:', error);
    }
  };

  const getMarkedDates = () => {
    const marked = {};

    // Mark streak dates
    streakDates.forEach((date) => {
      marked[date] = {
        customStyles: {
          container: {
            backgroundColor: ypdGreen50,
            borderRadius: 20,
          },
          text: {
            color: theme.colors.text,
          },
        },
      };
    });

    // Mark today's date
    marked[today] = {
      customStyles: {
        container: {
          backgroundColor: streakDates.includes(today)
            ? ypdGreen50
            : 'transparent',
          borderRadius: 20,
          borderWidth: 2,
          borderColor: streakDates.includes(today) ? ypdBorderLight : ypdGreen,
        },
        text: {
          color: theme.colors.text,
        },
      },
    };

    if (selectedDate && selectedDate.trim() !== '') {
      const isSelectedToday = selectedDate === today;
      const isSelectedInStreak = streakDates.includes(selectedDate);

      marked[selectedDate] = {
        customStyles: {
          container: {
            backgroundColor:
              isSelectedInStreak ||
              (isSelectedToday && streakDates.includes(today))
                ? ypdGreen50
                : 'transparent',
            borderWidth: 2,
            borderColor: ypdGreen50,
            borderRadius: 20,
          },
          text: {
            color: theme.colors.text,
            fontWeight: 'normal',
          },
        },
      };
    }

    return marked;
  };

  const getWeekRange = (date) => {
    const dateObj = new Date(date);
    const day = dateObj.getDay();
    const diffToMonday = day === 0 ? -6 : 1 - day;
    const start = new Date(dateObj);
    start.setDate(dateObj.getDate() + diffToMonday);
    const end = new Date(start);
    end.setDate(start.getDate() + 6);

    const sameMonth = start.getMonth() === end.getMonth();

    const formatDate = (date) =>
      date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });

    const formatDay = (date) => date.getDate();
    const formatMonth = (date) =>
      date.toLocaleDateString('en-US', { month: 'short' });

    if (sameMonth) {
      return `${formatMonth(start)} ${formatDay(start)} - ${formatDay(end)}`;
    } else {
      return `${formatDate(start)} - ${formatDate(end)}`;
    }
  };

  const getMonthRange = (date) => {
    const dateObj = new Date(date);
    const start = new Date(dateObj.getFullYear(), dateObj.getMonth(), 1);

    const formatMonth = (date) =>
      date.toLocaleDateString('en-US', { month: 'short' });

    return `${formatMonth(start)} ${start.getFullYear()}`;
  };

  const renderCalendar = () => {
    const commonProps = {
      onDayPress: (day) => {
        setSelectedDate(day.dateString);
        if (onDateChange) onDateChange(day.dateString);
      },
      markedDates: getMarkedDates(),
      markingType: 'custom',
      firstDay: 1,
      theme: {
        dayTextColor: theme.colors.text,
        textSectionTitleColor: theme.colors.text,
        todayTextColor: theme.colors.text,
        dotColor: ypdGreen50,
        selectedDotColor: theme.colors.text,
        textDayHeaderColor: theme.colors.text,
        calendarBackground: 'transparent',
        selectedDayBackgroundColor: 'transparent',
        selectedDayTextColor: theme.colors.text,
        todayBackgroundColor: streakDates.includes(today)
          ? ypdGreen50
          : 'transparent',
      },
    };

    if (viewMode === 'week') {
      return (
        <View pointerEvents={isLocked ? 'none' : 'auto'}>
          <View style={{ height: 60 }}>
            <CalendarProvider date={currentDate || today}>
              <WeekCalendar
                headerStyle={{ display: 'none' }}
                {...commonProps}
              />
            </CalendarProvider>
          </View>
        </View>
      );
    }
    return (
      <View pointerEvents={isLocked ? 'none' : 'auto'}>
        <View>
          <Calendar
            {...commonProps}
            current={currentDate || today}
            key={currentDate || today}
            hideArrows={true}
            hideExtraDays={true}
            renderHeader={() => <View />}
          />
        </View>
      </View>
    );
  };

  return (
    <View
      style={[
        styles.calendarContainer,
        { backgroundColor: theme.colors.background },
      ]}
    >
      <View style={styles.spacerX} />
      <View style={styles.weekText}>
        <IconButton
          icon="chevron-left"
          iconColor={theme.colors.text}
          size={24}
          onPress={handleGoBack}
          style={styles.backIcon}
        />
        {viewMode === 'week' ? (
          <Heading
            text={getWeekRange(currentDate || today)}
            fontSize={24}
            fontFamily="Medium"
            color={theme.colors.text}
          />
        ) : (
          <Heading
            text={getMonthRange(currentDate || today)}
            fontSize={24}
            fontFamily="Medium"
            color={theme.colors.text}
          />
        )}
        <IconButton
          icon="chevron-right"
          iconColor={theme.colors.text}
          size={24}
          onPress={handleGoForward}
          style={styles.backIconRight}
        />
      </View>

      <View style={styles.buttonContainer}>
        <View style={styles.buttonWrapper}>
          <ToggleButton
            options={[
              { label: 'Week', value: 'week' },
              { label: 'Month', value: 'month' },
            ]}
            selectedValue={viewMode}
            onPress={(val) => {
              setViewMode(val);
              setSelectedDate('');
              if (onViewModeChange) onViewModeChange(val);
            }}
          />
        </View>
      </View>

      <View
        style={[
          viewMode === 'month' ? { minHeight: 315 } : { minHeight: 85 },
          !theme.dark && styles.calendarShadowContainer,
        ]}
      >
        {renderCalendar()}
      </View>
    </View>
  );
};

export default CalendarComponent;
