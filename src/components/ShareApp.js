import { useState, useRef, useContext, useCallback } from 'react';
import { View, SafeAreaView, ScrollView, Alert, Share } from 'react-native';
import QRCode from 'react-native-qrcode-svg';
import { ThemeContext } from '../context/ThemeContext';
import ViewShot from 'react-native-view-shot';
import IconMaterial from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';
import Heading from '../components/Heading';
import CustomButton from '../components/CustomButton';
import BackButton from '../components/BackButton';
import { ypdGreen, ypdTeal, ypdWhite } from '../utils/colors';
import { useNavigation } from '@react-navigation/native';
import styles from '../assets/styles/ShareApp.scss';

const ShareApp = () => {
  const appName = 'YPD App';
  const { theme } = useContext(ThemeContext);
  const appStoreUrl = process.env.APP_DOWNLOAD_LINK;

  const [isSharing, setIsSharing] = useState(false);
  const viewShotRef = useRef(null);
  const navigation = useNavigation();
  const gradientColors = [ypdTeal, ypdGreen];

  const shareMessage = `Check out the YPD App! Download it here: ${appStoreUrl} `;

  const handleShare = useCallback(async () => {
    setIsSharing(true);
    try {
      const qrUri = viewShotRef.current
        ? await viewShotRef.current.capture()
        : null;

      const result = await Share.share({
        message: shareMessage,
        url: appStoreUrl,
        title: `Share ${appName}`,
        ...(qrUri && { url: qrUri }),
      });

      if (result.action === Share.sharedAction) {
        console.log('App shared successfully');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to share the app');
      console.error('Share error:', error);
    } finally {
      setIsSharing(false);
    }
  }, []);

  return (
    <SafeAreaView
      style={[styles.mainWrapper, { backgroundColor: theme.colors.background }]}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.mainContainer}>
          <View style={styles.upperContent}>
            <View style={styles.header}>
              <View style={styles.iconWrapper}>
                <LinearGradient
                  colors={gradientColors}
                  style={styles.gradientCircle}
                >
                  <IconMaterial name="share" size={40} color={ypdWhite} />
                </LinearGradient>
                <Heading
                  text={`Share ${appName} with your friends!`}
                  size="sm"
                  style={{ color: theme.colors.text }}
                />
              </View>
            </View>

            <View style={styles.container}>
              <ViewShot
                ref={viewShotRef}
                style={[styles.themeCard, styles.centeredCard]}
              >
                <View style={styles.cardContent}>
                  <QRCode
                    value={appStoreUrl}
                    size={280}
                    color={theme.colors.text}
                    backgroundColor={theme.colors.background}
                  />
                </View>
              </ViewShot>

              <View style={styles.themesContainer}>
                <CustomButton
                  title={isSharing ? 'Sharing...' : 'Share App'}
                  variant="green"
                  onPress={handleShare}
                  disabled={isSharing}
                  width="80%"
                  textColor={ypdWhite}
                />
              </View>
            </View>
          </View>

          <View style={styles.backButtonWrapper}>
            <BackButton onBackPress={() => navigation.navigate('Settings')} />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default ShareApp;
