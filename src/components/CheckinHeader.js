import { View } from 'react-native';
import Heading from './Heading';
import { Fonts } from '../utils';
import styles from '../assets/styles/SideEffects';
import { useContext } from 'react';
import { ThemeContext } from '../context/ThemeContext';

const CheckinHeader = ({ text, textAlign, style }) => {
  const { theme } = useContext(ThemeContext);
  return (
    <View style={[styles.header, style]}>
      <Heading
        text={text}
        size="lg"
        fontFamily={Fonts.MEDIUM}
        textAlign={textAlign}
        color={theme.colors.text}
      />
    </View>
  );
};

export default CheckinHeader;
