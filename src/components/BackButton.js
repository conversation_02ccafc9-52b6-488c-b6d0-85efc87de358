import { useContext } from 'react';
import { TouchableOpacity } from 'react-native';
import Svg, { Path } from 'react-native-svg';
import { useNavigation } from '@react-navigation/native';
import { ThemeContext } from '../context/ThemeContext';

const BackButton = ({ onBackPress, disabled = false }) => {
  const { theme } = useContext(ThemeContext);
  const navigation = useNavigation();
  const handleBackPress = onBackPress || (() => navigation.goBack());

  const CustomArrowIcon = () => (
    <Svg width="40" height="24" viewBox="0 0 40 24">
      <Path
        d="M34 12H2"
        stroke={theme.colors.text}
        strokeWidth={2}
        strokeLinecap="round"
      />
      <Path
        d="M8 6L2 12L8 18"
        stroke={theme.colors.text}
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
        fill="none"
      />
    </Svg>
  );

  return (
    <TouchableOpacity
      disabled={disabled}
      onPress={handleBackPress}
      style={[disabled && { opacity: 0.5 }]}
    >
      <CustomArrowIcon />
    </TouchableOpacity>
  );
};

export default BackButton;
