import { <PERSON>, ScrollView, Modal, SafeAreaView } from 'react-native';
import { Fonts, isSmallDevice, resetState } from '../utils';
import { useContext, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { ypdWhite } from '../utils/colors';
import { signOut } from 'aws-amplify/auth';
import Heading from './Heading';
import CustomButton from './CustomButton';
import Logo from '../assets/svgs/profileBuilder/Logo';
import styles from '../assets/styles/ConfirmRash';
import { ThemeContext } from '../context/ThemeContext';

const ConfirmRash = () => {
  const { theme } = useContext(ThemeContext);
  const navigation = useNavigation();

  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);

  const handleYesPress = async () => {
    setModalVisible(true);
  };

  const handleNoPress = () => {
    navigation.navigate('Tabs', { screen: 'Progress' });
  };

  const handleModalClose = async () => {
    try {
      setLoading(true);
      await resetState(signOut);
      console.log('[ConfirmRash] Cleanup successful');
      navigation.replace('CloseAppWarning');
      setModalVisible(false);
    } catch (error) {
      console.error('[ConfirmRash] Error during cleanup:', error);
      setModalVisible(false);
    }
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: theme.colors.background }}>
      <View style={styles.safetyModalContainer}>
        <ScrollView contentContainerStyle={styles.modalContent}>
          <Logo height={80} width={80} />
          <View style={{ height: 50 }} />
          <View style={styles.modalIconContainer}>
            <Heading
              text="Your safety comes first!"
              fontSize={28}
              fontFamily={Fonts.BOLD}
              color={theme.colors.text}
            />
          </View>
          <View style={styles.headingStyle}>
            <Heading
              text="If you develop a skin rash while using cannabis, this could be a serious problem. You should see a health care provider immediately and stop all cannabis. Make sure to tell your health care providers that you are allergic to cannabis."
              size="sm"
              fontFamily={Fonts.REGULAR}
              color={theme.colors.text}
            />
          </View>

          <View style={styles.headingStyle}>
            <Heading
              text="Please confirm that you have developed a skin rash."
              fontWeight={800}
              fontFamily={Fonts.BOLD}
              color={theme.colors.text}
            />
          </View>
          <View
            style={
              isSmallDevice ? styles.buttonWrapperSmall : styles.buttonWrapper
            }
          >
            <CustomButton
              title="I don't confirm"
              onPress={handleNoPress}
              width={150}
              variant={theme.dark ? 'green' : 'greenoutline'}
            />
            <CustomButton
              title="I confirm"
              onPress={handleYesPress}
              width={150}
            />
          </View>
        </ScrollView>
      </View>

      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.mainContainer}>
          <View
            style={[
              styles.modalContainer,
              { backgroundColor: theme.colors.background },
            ]}
          >
            <View style={styles.modalContent}>
              <Heading
                text="Urgent Action Required"
                size="md"
                color={theme.colors.text}
                fontFamily={Fonts.BOLD}
                textAlign="center"
                style={styles.modalHeader}
              />
              <Heading
                text="You should stop all cannabis right now and see a health care provider right now. Make sure to tell your health care providers that you are allergic to cannabis."
                size="sm"
                color={theme.colors.text}
                fontFamily={Fonts.REGULAR}
                style={styles.modalText}
              />
              <View style={styles.bottomButton}>
                <CustomButton
                  title="OK"
                  variant="teal"
                  onPress={handleModalClose}
                  width={120}
                  height={50}
                  color={ypdWhite}
                  activityIndicator={loading}
                />
              </View>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default ConfirmRash;
