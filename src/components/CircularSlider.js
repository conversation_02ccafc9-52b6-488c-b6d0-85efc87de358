import React, { useContext, useRef } from 'react';
import { View, PanResponder } from 'react-native';
import Svg, { Circle, LinearGradient, Stop } from 'react-native-svg';
import Heading from './Heading';
import {
  strokeIconColor,
  ypdCicleStroke,
  ypdTextGrey,
  ypdDarkGrey,
  ypdCicleFill,
  ypdGreen,
  ypdWhite,
  stopColor1,
  stopColor2,
  stopColor3,
  stopColor4,
  ypdBlack,
} from '../utils/colors';
import styles from '../assets/styles/CircularSlider';
import { gradientColorsPerSegment } from '../../src/utils/colors';
import { ratingInfo } from '../utils/utils';
import Header from './Header';
import { responsiveStyles } from '../utils';
import { ThemeContext } from '../context/ThemeContext';
const CircularSlider = ({ value, onChange, reverse = true, width }) => {
  const { theme } = useContext(ThemeContext);
  const strokeWidth = width < 250 ? 20 : 25;
  const radius = width / 2 - strokeWidth;
  const center = { x: width / 2, y: width / 2 };
  const circumference = 2 * Math.PI * radius;
  const steps = 5;

  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: () => true,
      onPanResponderMove: (event) => {
        const { locationX, locationY } = event.nativeEvent;
        updateAngleFromTouch(locationX, locationY);
      },
      onPanResponderRelease: (event) => {
        const { locationX, locationY } = event.nativeEvent;
        updateAngleFromTouch(locationX, locationY);
      },
    }),
  ).current;

  const updateAngleFromTouch = (x, y) => {
    const relX = x - center.x;
    const relY = y - center.y;
    let angle = Math.atan2(relY, relX) * (180 / Math.PI) + 90;
    if (angle < 0) angle += 360;

    const newValue = Math.min(
      Math.max(Math.round((angle / 360) * steps), 0),
      steps,
    );
    onChange(newValue);
  };

  const currentAngle = (value / steps) * 360;
  const angleRad = (currentAngle * Math.PI) / 180;

  const handleX = center.x + radius * Math.cos(angleRad - Math.PI / 2);
  const handleY = center.y + radius * Math.sin(angleRad - Math.PI / 2);

  const adjustedRatingInfo = reverse
    ? [{}, ...ratingInfo.slice(1).reverse()]
    : ratingInfo;

  const currentRating = adjustedRatingInfo[value] || adjustedRatingInfo[0];

  return (
    <View style={[styles.svgWrapper, { width: width }]}>
      {value !== 0 && currentRating && (
        <View style={styles.centerImageWrapper}>
          {currentRating.image}
          {currentRating.label && (
            <Heading
              text={`${currentRating.label}`}
              color={theme.colors.text}
              fontSize={responsiveStyles.fontSize}
            />
          )}
        </View>
      )}
      <Svg height={width} width={width} {...panResponder.panHandlers}>
        <Circle
          cx={center.x}
          cy={center.y}
          r={radius}
          fill="none"
          stroke={value === 0 ? ypdCicleStroke : strokeIconColor}
          strokeWidth={strokeWidth}
        />

        {[...gradientColorsPerSegment.entries()]
          .filter(([index]) => index < value)
          .reverse()
          .map(([index]) => (
            <React.Fragment key={index}>
              <LinearGradient
                id="fullGradient"
                x1="0%"
                y1="0%"
                x2="100%"
                y2="100%"
              >
                <Stop offset="100%" stopColor={stopColor1} />
                <Stop offset="75%" stopColor={stopColor2} />
                <Stop offset="50%" stopColor={stopColor3} />
                <Stop offset="25%" stopColor={stopColor4} />
                <Stop offset="0%" stopColor={ypdGreen} />
              </LinearGradient>

              <Circle
                cx={center.x}
                cy={center.y}
                r={radius}
                fill="none"
                stroke="url(#fullGradient)"
                strokeWidth={strokeWidth + 1}
                strokeDasharray={
                  (circumference * value) / steps + ', ' + circumference
                }
                strokeLinecap="round"
                transform={`rotate(-90, ${center.x}, ${center.y})`}
              />
            </React.Fragment>
          ))}
        {[...Array(steps + 1)].map((_, i) => {
          const angleDeg = (i / steps) * 360 - 90;
          const angleRadMarker = (angleDeg * Math.PI) / 180;
          const x = center.x + radius * Math.cos(angleRadMarker);
          const y = center.y + radius * Math.sin(angleRadMarker);

          return (
            <Circle
              key={i}
              cx={x}
              cy={y}
              r={6}
              fill={value >= i ? ypdCicleFill : ypdDarkGrey}
              stroke={value >= i ? ypdCicleFill : ypdDarkGrey}
              strokeWidth={1}
            />
          );
        })}

        <Circle
          cx={handleX}
          cy={handleY}
          r={width < 250 ? 15 : 20}
          fill={ypdGreen}
          stroke={ypdWhite}
          strokeWidth={3}
        />
      </Svg>
      <View
        style={{
          position: 'absolute',
          top: handleY - (width < 250 ? 15 : 20),
          left: handleX - (width < 250 ? 15 : 20),
          width: width < 250 ? 30 : 40,
          height: width < 250 ? 30 : 40,
          borderRadius: width < 250 ? 15 : 20,
          backgroundColor: ypdGreen,
          borderWidth: 3,
          borderColor: ypdWhite,
          shadowColor: ypdBlack,
          shadowOffset: { width: 0, height: 0 },
          shadowOpacity: 0.4,
          shadowRadius: 6,
          elevation: 10,
          zIndex: 10,
        }}
      />
    </View>
  );
};

const CircleSlider = ({
  value,
  onChange,
  reverse = false,
  heading,
  subheading,
  width,
}) => {
  const { theme } = useContext(ThemeContext);
  return (
    <View style={styles.wrapper}>
      <View style={styles.headerContainer}>
        <View style={styles.container}>
          <Header
            heading={heading}
            subHeading={subheading}
            style={styles.heading}
          />
        </View>
      </View>
      <View style={styles.headingText}>
        <Heading text="Slide to rate " size="sm" color={theme.colors.text} />
      </View>
      <View style={[styles.circularSliderWrapper, { width, height: width }]}>
        <CircularSlider
          value={value}
          onChange={onChange}
          reverse={reverse}
          width={width}
        />
      </View>
    </View>
  );
};

export default CircleSlider;
