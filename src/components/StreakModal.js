import { useContext } from 'react';
import { Modal, View, Text, Platform } from 'react-native';
import styles from '../assets/styles/StreakModal';
import DoubleTick from '../assets/svgs/checkin/DoubleTick';
import Thunder from '../assets/svgs/checkin/Thunder';
import Heading from './Heading';
import { Fonts } from '../utils';
import { IconButton } from 'react-native-paper';
import DeviceInfo from 'react-native-device-info';
import { toWords } from '../utils/utils';
import { ThemeContext } from '../context/ThemeContext';

const StreakModal = ({ visible, onClose, streak, totalCheckins }) => {
  const { theme } = useContext(ThemeContext);

  const deviceModel = DeviceInfo.getModel();
  const isIphoneSE = deviceModel.includes('iPhone SE');

  const streakMessage =
    streak === 7
      ? '🎉 Congrats on achieving a weekly streak.'
      : `${toWords(7 - streak)} check-ins down this week!`;

  return (
    <View
      style={[styles.container, { backgroundColor: theme.colors.background }]}
    >
      <Modal
        transparent={true}
        animationType="none"
        visible={visible}
        onRequestClose={() => {
          onClose(false);
        }}
      >
        <View style={styles.overlay}>
          <View
            style={[
              styles.modalContent,
              { backgroundColor: theme.colors.background },
              {
                marginBottom:
                  Platform.OS === 'android'
                    ? isIphoneSE
                      ? 75
                      : 63
                    : isIphoneSE
                      ? 75
                      : 95,
              },
            ]}
          >
            <View style={styles.closeIcon}>
              <IconButton
                icon="close"
                onPress={() => {
                  onClose(false);
                }}
                iconColor={theme.colors.text}
                style={styles.closeIcon}
              />
            </View>

            <Heading
              text={streakMessage}
              size="md"
              style={styles.title}
              fontWeight="600"
              color={theme.colors.text}
            />

            <View style={styles.dailyWeeklyContainer}>
              <View style={styles.dailyWeekly}>
                <DoubleTick />
                <Heading
                  text={totalCheckins}
                  size="md"
                  fontFamily={Fonts.MEDIUM}
                  color={theme.colors.text}
                />
                <Heading
                  text="Daily check-ins"
                  size="small"
                  color={theme.colors.text}
                />
              </View>

              <View style={styles.dailyWeekly}>
                <Thunder />
                <Heading
                  text={streak}
                  size="md"
                  fontFamily={Fonts.MEDIUM}
                  color={theme.colors.text}
                />
                <Heading
                  text="Weekly streaks"
                  size="small"
                  color={theme.colors.text}
                />
              </View>
            </View>
            <View style={styles.cardContainer}>
              <Heading
                text={
                  <Text style={{ fontFamily: Fonts.REGULAR }}>
                    Users who{' '}
                    <Text style={{ fontFamily: Fonts.MEDIUM }}>
                      stick with it daily for 40 days
                    </Text>{' '}
                    {'\n'}report better sleep, less stress, and{'\n'} improved
                    relief 80% of the time.{'\n'}You’ve got this!
                  </Text>
                }
                fontFamily={Fonts.MEDIUM}
                textAlign={'center'}
                color={theme.colors.text}
              />
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default StreakModal;
