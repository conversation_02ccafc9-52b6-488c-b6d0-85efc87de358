import { useContext } from 'react';
import { Button } from 'react-native-paper';
import { View, Text, Modal } from 'react-native';
import { ypdOldGreen } from '../utils/colors';
import styles from '../assets/styles/SuggestionPopup';
import { ThemeContext } from '../context/ThemeContext';
import DoseCheckin from '../assets/svgs/home/<USER>';
import { Fonts } from '../utils';

const BulletPoint = ({ text, theme }) => (
  <View style={styles.bulletPoints}>
    <Text style={[styles.bulletPoint, { color: theme.colors.text }]}>•</Text>
    <Text style={[styles.bulletPointText, { color: theme.colors.text }]}>
      {text}
    </Text>
  </View>
);

const SuggestionPopup = ({
  visible,
  onClose,
  data = [],
  isClose,
  cannabisType,
  dosageType,
}) => {
  const { theme } = useContext(ThemeContext);

  const isCheckinPopup = !!cannabisType && !!dosageType;
  const doseTypeText =
    cannabisType && dosageType
      ? `${cannabisType.toUpperCase()} ${dosageType.charAt(0).toUpperCase() + dosageType.slice(1)}`
      : 'your dose';

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalBackground}>
        <View
          style={[
            styles.modalContainer,
            {
              paddingVertical: isCheckinPopup ? 30 : 20,
              paddingHorizontal: isCheckinPopup ? 30 : 20,
              backgroundColor: theme.colors.background,
            },
          ]}
        >
          {isCheckinPopup ? (
            <>
              <DoseCheckin style={styles.doseCheckin} />
              <Text
                style={[
                  styles.titleText,
                  { color: theme.colors.text, fontFamily: Fonts.MEDIUM },
                ]}
              >
                Great! Your Dosage of {'\n'} {doseTypeText} has been{'\n'}
                successfully logged.
              </Text>
              <View style={styles.divider} />
              <Text style={[styles.judgmentText, { color: theme.colors.text }]}>
                Use your judgment about how you use this suggestion.
              </Text>
              <Text
                style={[
                  styles.infoText,
                  { color: theme.colors.text, fontFamily: Fonts.REGULAR },
                ]}
              >
                Oral cannabis may take up to 2 hours to take effect; inhaled
                cannabis, up to 1 hour.
              </Text>
              <Text
                style={[
                  styles.infoText,
                  { color: theme.colors.text, fontFamily: Fonts.REGULAR },
                ]}
              >
                If you think you may have medical emergency, immediately call
                your doctor or 911.
              </Text>

              <View style={styles.buttonContainer}>
                <Button
                  style={styles.button}
                  mode="outlined"
                  buttonColor={ypdOldGreen}
                  textColor={theme.colors.text}
                  onPress={onClose}
                >
                  {isClose ? 'Close' : `I understand let's go!`}
                </Button>
              </View>
            </>
          ) : (
            <>
              {data.map((item, index) => (
                <BulletPoint key={index} text={item} theme={theme} />
              ))}
              <View style={styles.buttonContainer}>
                <Button
                  style={styles.button}
                  mode="outlined"
                  buttonColor={ypdOldGreen}
                  textColor={theme.colors.text}
                  onPress={onClose}
                >
                  {isClose ? 'Close' : ' Agree'}
                </Button>
              </View>
            </>
          )}
        </View>
      </View>
    </Modal>
  );
};
export default SuggestionPopup;
