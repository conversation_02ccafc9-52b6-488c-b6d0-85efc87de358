import { View } from 'react-native';
import Svg, { Path, G, Text } from 'react-native-svg';
import { ypdBlack, ypdDefaultGreyGradient, ypdOldGreen } from '../utils/colors';
import styles from '../assets/styles/CircularTilesSlider.scss';
import Heading from './Heading';
import { Fonts } from '../utils';
import { toWords } from '../utils/utils';
import { ThemeContext } from '../context/ThemeContext';
import { useContext } from 'react';

const CIRCLE_SIZE = 180;
const STROKE_WIDTH = 17;
const SEGMENTS = 7;
const GAP_DEGREES = 15;

const radius = (CIRCLE_SIZE - STROKE_WIDTH) / 2;
const center = CIRCLE_SIZE / 2;
const totalGap = SEGMENTS * GAP_DEGREES;
const segmentAngle = (360 - totalGap) / SEGMENTS;

const polarToCartesian = (cx, cy, r, angleDeg) => {
  const angleRad = ((angleDeg - 90) * Math.PI) / 180.0;
  return {
    x: cx + r * Math.cos(angleRad),
    y: cy + r * Math.sin(angleRad),
  };
};

const describeArc = (cx, cy, r, startAngle, endAngle) => {
  const start = polarToCartesian(cx, cy, r, startAngle);
  const end = polarToCartesian(cx, cy, r, endAngle);
  const largeArc = endAngle - startAngle <= 180 ? '0' : '1';

  return ['M', start.x, start.y, 'A', r, r, 0, largeArc, 1, end.x, end.y].join(
    ' ',
  );
};

const CircularTileSlider = ({ streakCount }) => {
  const { theme } = useContext(ThemeContext);
  const filledSteps = Math.min(Math.max(Number(streakCount) || 0, 0), SEGMENTS);

  const streakMessage =
    streakCount === 7
      ? '🎉 Congrats on achieving a weekly streak.'
      : `${toWords(7 - streakCount)} check-ins down this week!`;

  return (
    <View style={styles.container}>
      <Heading
        text={streakMessage}
        fontSize={18}
        fontFamily={Fonts.MEDIUM}
        color={theme.colors.text}
      />
      <View style={styles.circularTileWrapper}>
        <Svg width={CIRCLE_SIZE} height={CIRCLE_SIZE}>
          <G rotation={20} originX={center} originY={center}>
            {Array.from({ length: SEGMENTS }).map((_, i) => {
              const start = i * (segmentAngle + GAP_DEGREES);
              const end = start + segmentAngle;
              const filled = i < filledSteps;

              return (
                <Path
                  key={i}
                  d={describeArc(center, center, radius, start, end)}
                  stroke={filled ? ypdOldGreen : ypdDefaultGreyGradient}
                  strokeWidth={STROKE_WIDTH}
                  fill="none"
                  strokeLinecap="round"
                />
              );
            })}
          </G>
          <Text
            x={center}
            y={center}
            fontSize="48"
            fontWeight="bold"
            fill={theme.colors.text}
            textAnchor="middle"
          >
            {`${filledSteps}/${SEGMENTS}`}
          </Text>
          <Text
            x={center}
            y={center + 30}
            fontSize="20"
            fontWeight="500"
            fill={theme.colors.text}
            textAnchor="middle"
          >
            weekly goal
          </Text>
        </Svg>
      </View>
    </View>
  );
};

export default CircularTileSlider;
