import { SafeAreaView, TouchableOpacity, View } from 'react-native';
import { AnimatedCircularProgress } from 'react-native-circular-progress';
import { useContext, useState } from 'react';
import { useSelector } from 'react-redux';
import styles from '../assets/styles/Streak';
import { Shadow } from 'react-native-shadow-2';
import Heading from './Heading';
import { Fonts } from '../utils';
import { ypdMidGrey, ypdOldGreen, ypdShadow } from '../utils/colors';
import StreakModal from './StreakModal';
import { responsiveStyles } from '../utils';
import { getCurrentWeekRange } from '../utils/utils';
import { ThemeContext } from '../context/ThemeContext';

const Streak = () => {
  const { theme } = useContext(ThemeContext);

  const [modalVisible, setModalVisible] = useState(false);

  const { checkinStats = {} } = useSelector((state) => state.suggestions || {});
  const { dailyCheckins, weeklyStreaks, fullStreakWeeks } = checkinStats;

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.shadowContainer}>
        <Shadow
          offset={[0, 3]}
          style={{ borderRadius: 6 }}
          distance={4}
          startColor={ypdShadow}
          stretch={true}
          radius={6}
        >
          <View
            style={[
              styles.cardsBorderRadius,
              { backgroundColor: theme.colors.background },
            ]}
          >
            <TouchableOpacity onPress={() => setModalVisible(true)}>
              <StreakModal
                visible={modalVisible}
                streak={fullStreakWeeks}
                totalCheckins={dailyCheckins}
                onClose={() => setModalVisible(false)}
              />
              <View style={styles.card}>
                <View style={styles.streakContainer}>
                  <AnimatedCircularProgress
                    size={70}
                    width={8}
                    fill={(weeklyStreaks / 7) * 100}
                    tintColor={ypdOldGreen}
                    backgroundColor={ypdMidGrey}
                    rotation={0}
                  >
                    {() => (
                      <Heading
                        text={`${weeklyStreaks || 0}/7`}
                        size="md"
                        fontFamily={Fonts.BOLD}
                        style={{ color: ypdOldGreen }}
                      />
                    )}
                  </AnimatedCircularProgress>
                  <View style={styles.streakTextContainer}>
                    <Heading
                      text="Stay on track and feel the progress."
                      color={theme.colors.placeholderTextColor}
                      fontFamily={Fonts.SEMIBOLD}
                      style={{ fontSize: responsiveStyles.fontSize }}
                    />
                    <Heading
                      text={getCurrentWeekRange()}
                      fontFamily={Fonts.REGULAR}
                      color={ypdOldGreen}
                      style={{
                        fontSize: responsiveStyles.fontSize,
                      }}
                    />
                  </View>
                </View>
              </View>
            </TouchableOpacity>
          </View>
        </Shadow>
      </View>
    </SafeAreaView>
  );
};

export default Streak;
