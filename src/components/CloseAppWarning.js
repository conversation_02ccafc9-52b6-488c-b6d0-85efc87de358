import { useEffect } from 'react';
import { View, BackHandler } from 'react-native';
import styles from '../assets/styles/CloseAppWarning';
import Heading from './Heading';
import WhiteAlert from '../assets/svgs/profileBuilder/WhiteAlert';

const CloseAppWarning = () => {
  useEffect(() => {
    const backAction = () => {
      return true;
    };

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );

    return () => backHandler.remove();
  }, []);

  return (
    <View style={styles.container}>
      <WhiteAlert style={styles.image} />
      <Heading
        text={`We are sorry. YPD can't help you.\n Please close the app.`}
        fontFamily="regular"
        style={styles.heading}
      />
    </View>
  );
};

export default CloseAppWarning;
