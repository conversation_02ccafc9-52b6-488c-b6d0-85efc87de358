import { useContext, useState } from 'react';
import { TextInput, View, TouchableOpacity } from 'react-native';
import styles from '../assets/styles/Input';
import { ypdTextGrey, ypdWhite, ypdOldGreen, ypdBlack } from '../utils/colors';
import { IconButton } from 'react-native-paper';
import { Fonts } from '../utils';
import { ThemeContext } from '../context/ThemeContext';

const Input = ({
  value,
  onChangeText,
  placeholder,
  onSubmitEditing,
  returnKeyType,
  onFocus,
  onBlur,
  keyboardType = 'default',
  autoCapitalize = 'sentences',
  autoCorrect = false,
  accessibilityLabel,
  secureTextEntry,
  iconName,
  onIconPress,
  iconSize = 26,
  iconColor = ypdTextGrey,
  showIcon = false,
  maxLength,
  hasValue = false,
  style,
  height = 49,
  borderRadius = 30,
  fontFamily = Fonts.REGULAR,
  textAlign = 'left',
  multiline = false,
  textAlignVertical,
  containerStyle,
  disabled = false,
}) => {
  const { theme } = useContext(ThemeContext);

  const [isFocused, setIsFocused] = useState(false);

  const handleFocus = () => {
    setIsFocused(true);
    onFocus?.();
  };

  const handleBlur = () => {
    setIsFocused(false);
    onBlur?.();
  };

  return (
    <View style={[styles.container, containerStyle]}>
      <TextInput
        style={[
          styles.input,
          isFocused
            ? [
                styles.inputFocused,
                {
                  borderColor: theme.dark
                    ? ypdOldGreen
                    : styles.inputFocused.borderColor,
                },
              ]
            : hasValue
              ? [
                  styles.inputWithValue,
                  {
                    borderColor: theme.dark
                      ? ypdWhite
                      : styles.inputWithValue.borderColor,
                  },
                ]
              : {
                  borderColor: theme.dark ? ypdWhite : styles.input.borderColor,
                },
          style,
          {
            height: height,
            borderRadius: borderRadius,
            fontFamily: fontFamily,
            color: theme.dark ? ypdWhite : ypdBlack,
          },
          disabled && { opacity: 0.6 },
        ]}
        textAlign={textAlign}
        placeholder={placeholder}
        placeholderTextColor={theme.colors.placeholderTextColor}
        value={value}
        onChangeText={onChangeText}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onSubmitEditing={onSubmitEditing}
        returnKeyType={returnKeyType}
        keyboardType={keyboardType}
        autoCapitalize={autoCapitalize}
        autoCorrect={autoCorrect}
        accessibilityLabel={accessibilityLabel}
        secureTextEntry={secureTextEntry}
        maxLength={maxLength}
        multiline={multiline}
        textAlignVertical={textAlignVertical}
        editable={!disabled}
      />
      {showIcon && iconName && (
        <TouchableOpacity
          onPress={onIconPress}
          style={styles.iconButton}
          disabled={disabled}
        >
          <IconButton icon={iconName} size={iconSize} iconColor={iconColor} />
        </TouchableOpacity>
      )}
    </View>
  );
};

export default Input;
