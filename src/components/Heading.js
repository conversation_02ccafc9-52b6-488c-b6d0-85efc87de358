import { Text, TouchableOpacity } from 'react-native';
import { useTheme } from '@react-navigation/native';
import styles from '../assets/styles/Heading';
import { Fonts } from '../utils';
import { ThemeContext } from '../context/ThemeContext';
import { useContext } from 'react';

const Heading = ({
  text,
  size = 'sm',
  style,
  color,
  fontWeight,
  width,
  fontFamily = Fonts.REGULAR,
  textAlign,
  fontSize,
  onPress,
  disabled = false,
}) => {
  const { colors } = useTheme();
  const textColor = color || colors.text;
  const { theme } = useContext(ThemeContext);

  const textStyles = {
    lg: styles.large,
    md: styles.medium,
    sm: styles.small,
    link: styles.link,
    xsmall: styles.xSmall,
    xssmall: styles.xsSmall,
  };

  const baseStyle = [
    styles.base,
    fontSize ? { fontSize } : textStyles[size],
    { color: textColor, fontWeight, width, fontFamily, textAlign },
    style,
  ];

  const renderContent = () => {
    if (typeof text === 'string' && text.startsWith('Q:')) {
      return (
        <>
          <Text style={[{ color: theme.colors.ypdPrimary }, styles.base]}>
            Q:
          </Text>
          <Text style={[{ color: textColor }, styles.base]}>
            {text.slice(2)}
          </Text>
        </>
      );
    }

    return text;
  };

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={onPress}
      disabled={!onPress || disabled}
    >
      <Text style={baseStyle}>{renderContent()}</Text>
    </TouchableOpacity>
  );
};

export default Heading;
