import { View, TouchableOpacity, Platform } from 'react-native';
import { Shadow } from 'react-native-shadow-2';
import Icon from 'react-native-vector-icons/Ionicons';
import Heading from './Heading';
import styles from '../assets/styles/Appearance.scss';
import { ypdGreen, ypdOldGreen, ypdWhite } from '../utils/colors';
import { Fonts } from '../utils';

const ThemeCard = ({
  themeType,
  title,
  description,
  isSelected,
  onPress,
  theme,
  iconName,
}) => (
  <Shadow
    distance={Platform.OS === 'android' ? 4 : 8}
    offset={[0, 2]}
    containerStyle={styles.shadowContainer}
    stretch={true}
  >
    <TouchableOpacity
      style={[
        styles.themeCard,
        isSelected && styles.selectedCard,
        { backgroundColor: theme.colors.lightTextGray },
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.cardContent}>
        <View
          style={[
            styles.phonePreview,
            themeType === 'dark'
              ? styles.darkPhonePreview
              : styles.lightPhonePreview,
          ]}
        >
          <View style={styles.phoneIcon}>
            <Icon
              name={iconName}
              size={20}
              color={iconName === 'sunny' ? ypdWhite : ypdGreen}
            />
          </View>
        </View>

        <View style={styles.themeInfo}>
          <Heading
            text={title}
            size="md"
            fontFamily={Fonts.BOLD}
            style={[{ color: theme.colors.text }]}
          />
          <Heading
            text={description}
            size="sm"
            fontFamily={Fonts.LIGHT}
            style={[{ color: theme.colors.text }]}
          />
        </View>

        {isSelected && (
          <View style={styles.checkmark}>
            <Icon name="checkmark-circle" size={24} color={ypdOldGreen} />
          </View>
        )}
      </View>
    </TouchableOpacity>
  </Shadow>
);

export default ThemeCard;
