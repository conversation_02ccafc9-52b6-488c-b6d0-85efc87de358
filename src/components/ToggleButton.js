import { View, TouchableOpacity } from 'react-native';
import { ypdBlack, ypdTextGrey } from '../utils/colors';
import { Fonts } from '../utils';
import Heading from './Heading';
import styles from '../assets/styles/ToggleButton.scss';

const ToggleButton = ({ options = [], selectedValue, onPress }) => {
  return (
    <View style={styles.toggleContainer}>
      {options.map((option) => (
        <TouchableOpacity
          key={option.value}
          style={[
            styles.toggleButton,
            selectedValue === option.value
              ? styles.activeButton
              : styles.inactiveButton,
          ]}
          onPress={() => onPress(option.value)}
        >
          <Heading
            text={option.label}
            fontSize={16}
            fontFamily={Fonts.MEDIUM}
            color={selectedValue === option.value ? ypdBlack : ypdTextGrey}
          />
        </TouchableOpacity>
      ))}
    </View>
  );
};

export default ToggleButton;
