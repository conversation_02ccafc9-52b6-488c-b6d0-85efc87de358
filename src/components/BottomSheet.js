import React, { useContext } from 'react';
import { View, Modal, TouchableOpacity } from 'react-native';
import { ThemeContext } from '../context/ThemeContext';
import styles from '../assets/styles/BottomSheet';
import { IconButton } from 'react-native-paper';

const BottomSheet = ({ visible, onClose, children }) => {
  const { theme } = useContext(ThemeContext);

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View
          style={[
            styles.sheetContainer,
            { backgroundColor: theme.colors.background },
          ]}
        >
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <IconButton
              icon="close"
              size={24}
              iconColor={theme.colors.text}
              onPress={onClose}
            />
          </TouchableOpacity>
          {children}
        </View>
      </View>
    </Modal>
  );
};

export default BottomSheet;
