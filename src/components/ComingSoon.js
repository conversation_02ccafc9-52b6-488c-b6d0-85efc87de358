import { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated } from 'react-native';
import Svg, {
  Circle,
  Path,
  Defs,
  LinearGradient,
  Stop,
  G,
  Polygon,
} from 'react-native-svg';
import Heading from './Heading';
import { Fonts } from '../utils';

const AnimatedPath = Animated.createAnimatedComponent(Path);

const ComingSoon = () => {
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const flameOpacity = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
      ]),
    );

    const fadeAnimation = Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1500,
      useNativeDriver: true,
    });

    const flameFlicker = Animated.loop(
      Animated.sequence([
        Animated.timing(flameOpacity, {
          toValue: 0.6,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(flameOpacity, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
      ]),
    );

    pulseAnimation.start();
    fadeAnimation.start();
    flameFlicker.start();

    return () => {
      pulseAnimation.stop();
      flameFlicker.stop();
    };
  }, []);

  const RocketSVG = () => (
    <Svg width="220" height="220" viewBox="0 0 120 120">
      <Defs>
        <LinearGradient id="rocketGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <Stop offset="0%" stopColor="#FF6B6B" />
          <Stop offset="100%" stopColor="#4ECDC4" />
        </LinearGradient>
      </Defs>
      <G>
        <Path d="M60 10 L70 80 L60 85 L50 80 Z" fill="url(#rocketGradient)" />
        <Polygon points="45,70 50,80 45,85" fill="#FF6B6B" />
        <Polygon points="75,70 70,80 75,85" fill="#FF6B6B" />
        <Circle cx="60" cy="35" r="8" fill="#87CEEB" />

        {/* 🔥 Animated Flames */}
        <AnimatedPath
          d="M55 85 L60 100 L65 85"
          fill="#FFA500"
          style={{ opacity: flameOpacity }}
        />
        <AnimatedPath
          d="M57 85 L60 95 L63 85"
          fill="#FF4500"
          style={{ opacity: flameOpacity }}
        />
      </G>
    </Svg>
  );

  return (
    <View style={styles.container}>
      <Animated.View style={[styles.content, { opacity: fadeAnim }]}>
        <Animated.View
          style={[
            styles.rocketContainer,
            { transform: [{ scale: pulseAnim }, { rotate: '15deg' }] },
          ]}
        >
          <RocketSVG />
        </Animated.View>

        <Heading
          text="Coming Soon"
          style={styles.title}
          fontFamily={Fonts.REGULAR}
        />
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0BDDB4',
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  rocketContainer: {
    marginBottom: 10,
  },
  title: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 20,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  subtitle: {
    fontSize: 24,
    color: '#FFFFFF',
    textAlign: 'center',
    opacity: 0.9,
  },
});

export default ComingSoon;
