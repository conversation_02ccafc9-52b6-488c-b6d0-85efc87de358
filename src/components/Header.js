import { View } from 'react-native';
import styles from '../assets/styles/Heading';
import Heading from './Heading';
import { Fonts } from '../utils';
import { ThemeContext } from '../context/ThemeContext';
import { useContext } from 'react';

const Header = ({
  heading,
  subHeading,
  style,
  sizeHeading = 'lg',
  sizeSubHeading = 'lg',
}) => {
  const { theme } = useContext(ThemeContext);
  return (
    <View style={styles.headWrapper}>
      <Heading
        text={heading}
        size={sizeHeading}
        fontFamily={Fonts.MEDIUM}
        style={style}
        color={theme.colors.text}
      />
      {subHeading && (
        <Heading
          text={subHeading}
          size={sizeSubHeading}
          fontFamily={Fonts.MEDIUM}
          style={style}
          color={theme.colors.text}
        />
      )}
    </View>
  );
};

export default Header;
