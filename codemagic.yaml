workflows:
  react-native-android:
    name: React Native Android
    max_build_duration: 120
    instance_type: mac_mini_m1
    triggering:
      events:
        - tag
    environment:
      android_signing:
        - upload-key
      groups:
        - staging
        - google_play
    scripts:
      - name: Set Android SDK location
        script: echo "sdk.dir=$ANDROID_SDK_ROOT" > "$CM_BUILD_DIR/android/local.properties"

      - name: Install dependencies
        script: yarn install --frozen-lockfile

      - name: Build Android release
        script: |
          echo $AWS_EXPORTS_BASE64 | base64 --decode > ./src/aws-exports.js
          LATEST_GOOGLE_PLAY_BUILD_NUMBER=$(google-play get-latest-build-number --package-name "$ANDROID_PACKAGE_NAME")

          if [ -z $LATEST_GOOGLE_PLAY_BUILD_NUMBER ]; then
              # fallback in case no build number was found from google play. Alternatively, you can `exit 1` to fail the build
              UPDATED_BUILD_NUMBER=$BUILD_NUMBER
          else
              UPDATED_BUILD_NUMBER=$(($LATEST_GOOGLE_PLAY_BUILD_NUMBER + 1))
          fi
          
          RELEASE="${APP_VERSION} (${UPDATED_BUILD_NUMBER})"
          npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res/

          cd android
          rm -rf app/src/main/res/drawable-hdpi/ app/src/main/res/drawable-mdpi/ app/src/main/res/drawable-xhdpi/ app/src/main/res/drawable-xxhdpi/ app/src/main/res/drawable-xxxhdpi/

          ./gradlew bundleRelease \
            -PversionCode=$UPDATED_BUILD_NUMBER \
            -PversionName=$APP_VERSION
    artifacts:
      - android/app/build/outputs/**/*.aab
    publishing:
      email:
        recipients:
          - <EMAIL>
        notify:
          success: true
          failure: true
      google_play:
        credentials: $GCLOUD_SERVICE_ACCOUNT_CREDENTIALS
        track: internal
        submit_as_draft: true

  react-native-ios:
    name: React Native iOS
    max_build_duration: 120
    instance_type: mac_mini_m1
    triggering:
      events:
        - tag
    integrations:
      app_store_connect: ypd_inc_api_key
    environment:
      groups:
        - staging
      ios_signing:
        distribution_type: app_store
        bundle_identifier: $IOS_PACKAGE_NAME
      vars:
        XCODE_SCHEME: "YPD"
        XCODE_WORKSPACE: "YPD.xcworkspace"
    scripts:
      - name: Install dependencies
        script: yarn install --frozen-lockfile

      - name: Sentry Integration
        script: |
          echo "defaults.url=https://sentry.io/" > android/sentry.properties
          echo "defaults.org=$SENTRY_ORG" >> android/sentry.properties
          echo "defaults.project=$SENTRY_PROJECT" >> android/sentry.properties
          echo "auth.token=$SENTRY_AUTH_TOKEN" >> android/sentry.properties
          echo "defaults.url=https://sentry.io/" > ios/sentry.properties
          echo "defaults.org=$SENTRY_ORG" >> ios/sentry.properties
          echo "defaults.project=$SENTRY_PROJECT" >> ios/sentry.properties
          echo "auth.token=$SENTRY_AUTH_TOKEN" >> ios/sentry.properties

      - name: Set up AWS environment
        script: echo $AWS_EXPORTS_BASE64 | base64 --decode > ./src/aws-exports.js

      - name: Install CocoaPods dependencies
        script: cd ios && pod install

      - name: Set up provisioning profiles
        script: xcode-project use-profiles --project ios/*.xcodeproj

      - name: Build iOS release
        script: |
          cd $CM_BUILD_DIR/ios
          LATEST_BUILD_NUMBER=$(app-store-connect get-latest-testflight-build-number "$APPLE_STORE_ID")
          agvtool new-version -all $(($LATEST_BUILD_NUMBER + 1))
          agvtool new-marketing-version $APP_VERSION

          UPDATED_BUILD_NUMBER=$(($LATEST_BUILD_NUMBER + 1))
          RELEASE="${APP_VERSION} (${UPDATED_BUILD_NUMBER})"

          cd $CM_BUILD_DIR
          xcode-project build-ipa \
            --workspace "$CM_BUILD_DIR/ios/$XCODE_WORKSPACE" \
            --scheme "$XCODE_SCHEME"

    artifacts:
      - build/ios/ipa/*.ipa
      - /tmp/xcodebuild_logs/*.log
      - $HOME/Library/Developer/Xcode/DerivedData/**/Build/**/*.app
      - $HOME/Library/Developer/Xcode/DerivedData/**/Build/**/*.dSYM

    publishing:
      scripts:
        - name: Sentry upload artifacts
          script: |
            echo "Find build artifacts"
            dsymPath=$(find $CM_BUILD_DIR/build/ios/xcarchive/*.xcarchive -name "*.dSYM" | head -1)
            if [[ -z ${dsymPath} ]]
            then
            echo "No debug symbols were found, skip publishing to Sentry"
            else
            echo "Publishing debug symbols from $dsymPath to Sentry"
            sentry-cli --auth-token $SENTRY_AUTH_TOKEN upload-dif \
            --org $SENTRY_ORG \
            --project $SENTRY_PROJECT $dsymPath
            fi
      email:
        recipients:
          - <EMAIL>
        notify:
          success: true
          failure: true
      app_store_connect:
        auth: integration
        submit_to_testflight: true
