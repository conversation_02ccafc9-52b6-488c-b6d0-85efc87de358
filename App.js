import { useState, useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { Provider } from 'react-redux';
import awsConfig from './src/aws-exports.js';
import { Amplify } from 'aws-amplify';
import * as Sentry from '@sentry/react-native';
import { store, persistor } from './src/redux/store';
import { PersistGate } from 'redux-persist/integration/react';
import { AuthProvider } from './src/context/AuthContext';
import { Geofencing } from './src/utils/geofencing.js';
import { ThemeProvider } from './src/context/ThemeContext';
import { loadTheme } from './src/utils/theme/themeLoader';

try {
  Amplify.configure(awsConfig);
} catch (error) {
  console.error('Error configuring Amplify:', error);
}

Sentry.init({
  dsn: process.env.SENTRY_DSN,

  // uncomment the line below to enable Spotlight (https://spotlightjs.com)
  // spotlight: __DEV__,
});

function MainWrapper({ initialTheme }) {
  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <ThemeProvider initialTheme={initialTheme}>
          <AuthProvider>
            <NavigationContainer theme={initialTheme.theme}>
              <Geofencing />
            </NavigationContainer>
          </AuthProvider>
        </ThemeProvider>
      </PersistGate>
    </Provider>
  );
}

export default Sentry.wrap(() => {
  const [ready, setReady] = useState(false);
  const [initialTheme, setInitialTheme] = useState(null);

  useEffect(() => {
    loadTheme().then((theme) => {
      setInitialTheme(theme);
      setReady(true);
    });
  }, []);

  if (!ready) return null;

  return <MainWrapper initialTheme={initialTheme} />;
});
