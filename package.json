{"name": "YPD", "version": "0.0.1", "private": true, "type": "commonjs", "scripts": {"android": "react-native run-android", "format": "prettier --write 'src/**/*.{js,jsx,ts,tsx}'", "ios": "react-native run-ios", "lint": "eslint 'src/**/*.{js,jsx,ts,tsx}'", "prepare": "husky", "start": "react-native start", "test": "jest", "postinstall": "patch-package"}, "dependencies": {"@aws-amplify/analytics": "^7.0.60", "@aws-amplify/cli": "^12.13.0", "@aws-amplify/core": "^6.11.4", "@aws-amplify/react-native": "^1.1.6", "@aws-amplify/rtn-web-browser": "^1.1.1", "@aws-amplify/storage": "^6.7.10", "@react-native-assets/slider": "^9.1.0", "@react-native-async-storage/async-storage": "2.1.0", "@react-native-community/datetimepicker": "^8.2.0", "@react-native-community/netinfo": "11.4.1", "@react-native-community/push-notification-ios": "^1.11.0", "@react-navigation/bottom-tabs": "^6.5.5", "@react-navigation/drawer": "^6.6.0", "@react-navigation/native": "^6.1.4", "@react-navigation/native-stack": "^6.9.10", "@react-navigation/stack": "^7.0.14", "@reduxjs/toolkit": "^2.3.0", "@sentry/react-native": "^6.10.0", "@sharcoux/slider": "8.0.6", "aws-amplify": "^6.14.4", "aws-amplify-react-native": "^7.0.2", "axios": "^1.7.8", "babel-plugin-react-native-classname-to-style": "^1.2.2", "babel-plugin-react-native-platform-specific-extensions": "^1.1.1", "moment": "^2.30.1", "react": "18.3.1", "react-native": "0.76.3", "react-native-calendars": "^1.1312.0", "react-native-chart-kit": "6.12.0", "react-native-circular-progress": "^1.4.1", "react-native-device-info": "^14.0.1", "react-native-dotenv": "^3.4.11", "react-native-element-dropdown": "^2.12.2", "react-native-file-viewer": "^2.1.5", "react-native-fs": "^2.20.0", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "2.21.2", "react-native-get-random-values": "^1.11.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-keychain": "^10.0.0", "react-native-linear-gradient": "^2.8.3", "react-native-modal-datetime-picker": "^18.0.0", "react-native-paper": "5.12.5", "react-native-popup-dialog": "0.18.3", "react-native-progress": "^5.0.1", "react-native-push-notification": "^8.1.1", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "3.17.1", "react-native-render-html": "6.3.4", "react-native-responsive-fontsize": "0.5.1", "react-native-safe-area-context": "4.14.0", "react-native-sass-transformer": "^3.0.0", "react-native-screens": "^4.3.0", "react-native-shadow-2": "^7.1.1", "react-native-svg": "^15.8.0", "react-native-uuid": "^2.0.3", "react-native-vector-icons": "^10.2.0", "react-native-view-shot": "^4.0.3", "react-native-webview": "13.12.4", "react-redux": "^9.1.2", "redux-persist": "^6.0.0", "redux-toolkit": "^1.1.2", "sass": "^1.86.0"}, "devDependencies": {"@aws-amplify/cli": "^12.13.0", "@babel/core": "^7.26.0", "@babel/eslint-parser": "^7.25.9", "@babel/preset-env": "^7.26.0", "@babel/preset-flow": "^7.25.9", "@babel/preset-react": "^7.25.9", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.1.2", "@react-native-community/cli-platform-android": "15.1.2", "@react-native-community/cli-platform-ios": "15.1.2", "@react-native/babel-preset": "^0.76.3", "@react-native/eslint-config": "0.76.3", "@react-native/metro-config": "0.76.3", "babel-eslint": "^10.1.0", "babel-jest": "^29.6.3", "eslint": "^9.13.0", "eslint-config-prettier": "^9.1.0", "eslint-define-config": "^2.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "husky": "^9.1.6", "jest": "^29.6.3", "lint-staged": "^15.2.10", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "prettier": "^3.4.1", "react-test-renderer": "18.3.1"}, "engines": {"node": ">=18"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["yarn format"]}}